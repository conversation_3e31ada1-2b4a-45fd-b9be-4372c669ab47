# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /


# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/tld_ai","http://127.0.0.1:9092/tld_ai"],["/upload","http://localhost:3300/upload"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://127.0.0.1:9092/tld_ai

#后台接口父地址(必填)
VITE_GLOB_API_URL=/tld_ai

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'


# 填写后将作为乾坤子应用启动，主应用注册时AppName需保持一致（放开 VITE_GLOB_QIANKUN_MICRO_APP_NAME 参数表示jeecg-vue3将以乾坤子应用模式启动）
#VITE_GLOB_QIANKUN_MICRO_APP_NAME=jeecg-vue3
# 作为乾坤子应用启动时必填，需与qiankun主应用注册子应用时填写的 entry 保持一致
#VITE_GLOB_QIANKUN_MICRO_APP_ENTRY=//localhost:3001/jeecg-vue3

# 全局隐藏哪些布局。可选属性：sider,header,multi-tabs；多个用逗号隔开
#VITE_GLOB_HIDE_LAYOUT_TYPES=sider,header,multi-tabs

# 在线文档编辑版本。可选属性：wps, offlineWps(离线版) ,onlyoffice
VITE_GLOB_ONLINE_DOCUMENT_VERSION=wps
#minio地址
VITE_GLOB_MINIO_URL=http://127.0.0.1:9000/
#低代码编辑器地址
VITE_GLOB_AMIS_URL=http://127.0.0.1:7996/
