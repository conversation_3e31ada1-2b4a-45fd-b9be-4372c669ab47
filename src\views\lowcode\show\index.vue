<template>
  <!-- 动态渲染：对象非空时才显示组件 -->
  <AmisRenderer v-if="!isSchemaEmpty" :schema="schema" :env="env" />

  <!-- 可选：显示空状态提示 -->
  <div v-else class="empty-container">
    <a-empty
      :image="netWorkSvg"
      :image-style="{
        height: '240px',
      }"
    >
      <template #description>
        <span> {{ errorText }} </span>
      </template>
    </a-empty>
  </div>
</template>

<script setup lang="ts" name="lowcode-show">
  import { ref, unref, reactive, onMounted, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { defHttp } from '@/utils/http/axios';
  import { AmisRenderer } from '/@/components/AmisRenderer';
  import netWorkSvg from '/@/assets/svg/net-error.svg';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';

  const route = useRoute();
  const routerId = ref<any>('');
  // 定义schema对象来存储页面结构数据
  const schema = reactive({});
  //顶层数据域的值，用于传递给amis组件
  const glob = useGlobSetting();
  const userStore = useUserStore();
  const env = reactive({
    baseUrl: glob.domainUrl, // 配置全局的baseUrl
    userInfo: {
      id: unref(userStore.getUserInfo).id,
      realname: unref(userStore.getUserInfo).realname,
      username: unref(userStore.getUserInfo).username,
    },
  });
  const errorText = ref<string>('请检查配页面置是否正确，或联系管理员进行配置！');
  /**
   * 加载页面结构数据
   *
   * @returns 无返回值
   */
  const loadSchema = async () => {
    try {
      const res = await defHttp.get({ url: '/lcd/page/queryById', params: { id: routerId.value } }, { isTransformResponse: false });

      if (!res?.success || !res.result?.jsonSchema) return;

      const parsedSchema = JSON.parse(res.result.jsonSchema);
      Object.keys(parsedSchema).forEach((key) => {
        schema[key] = parsedSchema[key];
      });
    } catch (e) {
      console.error('Failed to load schema:', e);
    }
  };
  // 计算属性：检测schema是否为空
  const isSchemaEmpty = computed(() => {
    return Object.keys(schema).length === 0;
  });
  onMounted(() => {
    routerId.value = route.fullPath.split('/').pop();
    loadSchema();
  });
</script>

<style scoped>
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
</style>
