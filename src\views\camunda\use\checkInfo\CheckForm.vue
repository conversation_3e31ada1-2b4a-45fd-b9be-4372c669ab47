<template>
  <div style="background-color: white">
    <a-card class="formcontainer" style="margin-top: 10px">
      <a-spin :spinning="confirmLoading">
        <a-form
          :model="formState"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 20 }"
          autocomplete="off"
          style="width: 550px"
          @finish="onFinish"
          @finishFailed="onFinishFailed"
        >
          <a-form-item label="审批类型" name="opType" :rules="[{ required: true, message: '请选择审批操作类型!' }]">
            <a-select ref="select" v-model:value="formState.opType" @change="handleChange">
              <a-select-option v-for="(item, key) in buttonArr" :key="key" :value="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="退回节点" name="backTaskKey" :rules="[backRule]" v-show="showBack">
            <a-select ref="select" v-model:value="formState.backTaskKey">
              <a-select-option v-for="(item, key) in preTaskArr" :key="key" :value="item.taskKey">
                {{ item.taskName }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="审批意见" name="opIdea" :rules="[{ required: true, message: '请输入审批意见!' }]">
            <a-textarea v-model:value="formState.opIdea" :auto-size="{ minRows: 5, maxRows: 8 }" />
          </a-form-item>
          <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
            <div style="text-align: center">
              <a-button type="primary" preIcon="ant-design:save-outlined" html-type="submit">提交</a-button>
            </div>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';

  // 声明Emits
  const emit = defineEmits(['submitTask']);
  //提示框组件对象
  const { createMessage } = useMessage();
  interface FormState {
    opType: string;
    backTaskKey: string;
    opIdea: string;
  }

  const formState = reactive<FormState>({
    opType: '',
    backTaskKey: '',
    opIdea: '',
  });

  enum Api {
    prop = '/act/manage/getTaskProps',
    preTasks = '/act/manage/preTasks',
    complete = '/act/manage/complete',
    reject = '/act/manage/reject',
    back = '/act/manage/backAny',
    submit = '/act/manage/submit',
  }

  const buttonArr = reactive<any[]>([]);
  const preTaskArr = reactive<any[]>([]);
  const confirmLoading = ref(false);
  const backRule = reactive({
    required: false,
    message: '请选择退回节点!',
  });
  const showBack = ref(false);
  const props = defineProps({
    businessKey: {
      type: String,
      default: '',
    },
    procInstId: {
      type: String,
      default: '',
    },
    procDefKey: {
      type: String,
      default: '',
    },
    procDefName: {
      type: String,
      default: '',
    },
    taskId: {
      type: String,
      default: '',
    },
    allowOperate: {
      type: Boolean,
      default: false,
    },
  });
  //输出化表单属性
  function initProps() {
    if (props.taskId != null && props.taskId != '') {
      let params = {
        procInstId: props.procInstId,
        taskId: props.taskId,
      };
      defHttp.get({ url: Api.prop, params }, { isTransformResponse: false }).then((res) => {
        if (res.success) {
          if (res.result != null) {
            let buttonAuth = res.result.buttonAuth;
            for (let key in buttonAuth) {
              if (buttonAuth[key] == 'Y') {
                let btn = getSingleBtn(key);
                buttonArr.push(btn);
              }
            }
          }
        } else {
          createMessage.warning(res.message);
        }
      });
    }
  }
  function initPreTasks() {
    if (props.taskId != null && props.taskId != '') {
      let params = {
        taskId: props.taskId,
      };
      defHttp.get({ url: Api.preTasks, params }, { isTransformResponse: false }).then((res) => {
        if (res.success) {
          Object.assign(preTaskArr, res.result);
        } else {
          createMessage.warning(res.message);
        }
      });
    }
  }
  function handleChange(value) {
    formState.backTaskKey = '';
    //如果是退回至任意节点
    if (value == 'back') {
      showBack.value = true;
      backRule.required = true;
    } else {
      showBack.value = false;
      backRule.required = false;
    }
  }
  function getOpDesc(key) {
    let idea = '';
    switch (key) {
      case 'submit':
        idea = '提交';
        break;
      case 'complete':
        idea = '审批通过';
        break;
      case 'reject':
        idea = '驳回至起点';
        break;
      case 'back':
        idea = '退回任意节点';
        break;
    }
    return idea;
  }
  function getSingleBtn(key) {
    let btn: any = {};
    switch (key) {
      case 'isSubmit':
        btn.key = 1;
        btn.value = 'submit';
        btn.text = '提交';
        break;
      case 'isComplete':
        btn.key = 2;
        btn.value = 'complete';
        btn.text = '审批通过';
        break;
      case 'isReject':
        btn.key = 3;
        btn.value = 'reject';
        btn.text = '驳回至起点';
        break;
      case 'isBackAny':
        btn.key = 4;
        btn.value = 'back';
        btn.text = '退回任意节点';
        break;
    }
    return btn;
  }
  const onFinish = (values: any) => {
    switch (formState.opType) {
      case 'submit':
        complete(formState.opType, Api.submit);
        break;
      case 'complete':
        complete(formState.opType, Api.complete);
        break;
      case 'reject':
        complete(formState.opType, Api.reject);
        break;
      case 'back':
        complete(formState.opType, Api.back);
        break;
    }
  };
  //当前节点提交
  function complete(opType, url) {
    let variables = [];
    let params: any = {
      taskId: props.taskId,
      opType: formState.opType,
      comment: formState.opIdea,
      opDesc: getOpDesc(formState.opType),
      variables: variables,
      backTaskKey: null,
    };
    if (opType == 'back') {
      params.backTaskKey = formState.backTaskKey;
    }
    confirmLoading.value = true;
    defHttp.post({ url: url, params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        confirmLoading.value = false;
        emit('submitTask');
        createMessage.success(res.message);
      } else {
        confirmLoading.value = false;
        createMessage.warning(res.message);
      }
    });
  }
  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };
  onMounted(() => {
    initProps();
    initPreTasks();
  });
</script>

<style>
  .formcontainer {
    display: flex; /* 设置容器为 flexbox 布局 */
    justify-content: center; /* 水平居中 */
  }
</style>
