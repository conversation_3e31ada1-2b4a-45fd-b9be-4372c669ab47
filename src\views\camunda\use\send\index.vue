<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #action="{ record }">
        <a @click="openCheckInfoModal(record)"> <BranchesOutlined style="margin-right: 3px; color: green" /><span style="color: green">详情</span></a>
      </template>
      <template #bodyCell="{ column, text }">
        <template v-if="column.key === 'state'">
          <span>
            <a-tag v-if="text == 'ACTIVE'" color="pink">未完成</a-tag>
            <a-tag v-if="text == 'COMPLETED'" color="green">已完成</a-tag>
          </span>
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script setup lang="ts" name="camunda-send">
  import { ref, unref, watch } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Table';
  import { BranchesOutlined } from '@ant-design/icons-vue';
  import { defHttp } from '/@/utils/http/axios';

  enum Api {
    list = '/act/manage/sendList',
  }
  const getList = (params) => {
    return defHttp.get({ url: Api.list, params });
  };
  const columns: BasicColumn[] = [
    {
      title: '流程名称 ',
      align: 'center',
      dataIndex: 'procDefName',
    },
    {
      title: '发起时间',
      align: 'center',
      dataIndex: 'startTime',
      width: 250,
    },
    {
      title: '结束时间',
      align: 'center',
      dataIndex: 'endTime',
      width: 250,
    },
    {
      title: '流程耗时',
      align: 'center',
      dataIndex: 'duration',
      width: 250,
      customRender(obj) {
        const ms = obj.value;
        if (ms != null) {
          const day = Math.floor(ms / (24 * 60 * 60 * 1000));
          const hour = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
          const minute = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
          const second = Math.floor((ms % (60 * 1000)) / 1000);
          let val = '';
          if (day != 0) {
            val = val + day + '天';
          }
          if (hour != 0) {
            val = val + hour + '时';
          }
          if (minute != 0) {
            val = val + minute + '分';
          }
          if (second != 0) {
            val = val + second + '秒';
          }
          return val;
        } else {
          return ms;
        }
      },
    },
    {
      title: '完成状态',
      align: 'center',
      dataIndex: 'state',
      width: 200,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'procDefName',
      label: '流程名称',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];
  const [registerTable, { setProps }] = useTable({
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: false,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  /**
   * 打开详情窗口
   */
  function openCheckInfoModal(record) {}

  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
  }
</style>
