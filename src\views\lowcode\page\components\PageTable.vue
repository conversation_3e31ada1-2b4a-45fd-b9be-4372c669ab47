<template>
  <BasicTable @register="registerTable" :rowSelection="false">
    <template #tableTitle>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">新增</a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'operation'">
        <a @click="handleDesign(record)"><a-icon type="codepen" style="margin-right: 3px" />设计</a>
        <a-divider type="vertical" />
        <a @click="handleEdit(record)"><a-icon type="edit" style="margin-right: 3px" />编辑</a>
        <a-divider type="vertical" />
        <a-popconfirm title="是否删除？" ok-text="是" cancel-text="否" @confirm="handleDelete(record)">
          <a><DeleteOutlined style="margin-right: 3px; color: #eb2f96" /><span style="color: #eb2f96">删除</span></a>
        </a-popconfirm>
      </template>
    </template>
  </BasicTable>
  <PageModal @register="registerModal" @success="reload" :isDisabled="isDisabled" :moduleId="moduleId" />
</template>

<script setup lang="ts" name="lowcode-module">
  import { ref, onMounted } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { columns, searchFormSchema } from '../page.data';
  import { getList, deletePage } from '../page.api';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import PageModal from './PageModal.vue';
  import { usePageContext } from '../pageContext';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import { getToken } from '/@/utils/auth';
  import { useGlobSetting } from '@/hooks/setting';

  const globSetting = useGlobSetting();
  const { createMessage: $message } = useMessage();
  //获取事件总线
  const { pageEmitter } = usePageContext();

  const [registerModal, { openModal }] = useModal();

  const moduleId = ref('');
  const isDisabled = ref(false);
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      api: getList,
      columns: columns,
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
      },
      //自定义默认排序
      defSort: {
        column: 'createTime',
        order: 'desc',
      },
      canResize: true, //可以自适应高度
      showActionColumn: false,
      // 请求之前对参数做处理
      beforeFetch(params) {
        params.moduleId = moduleId.value;
      },
    },
  });

  const [registerTable, { reload }] = tableContext;
  /**
   * 新增事件
   */
  function handleAdd() {
    if (moduleId.value == '') {
      $message.warning('请先选择业务模块！');
      return false;
    }
    isDisabled.value = false;
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 页面配置
   */
  function handleDesign(record) {
    // 新标签页打开
    window.open(`${globSetting.amisUrl}#/edit/page/${record.id}/${getToken()}`, '_blank');
  }
  /**
   * 编辑事件
   */
  function handleEdit(record) {
    isDisabled.value = false;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deletePage({ id: record.id }, reload);
  }

  onMounted(() => {
    //选中sheet页配置数据触发事件
    pageEmitter.on('on-module-click', (key) => {
      moduleId.value = key;
      reload();
    });
  });
</script>

<style lang="less"></style>
