import { BasicColumn, FormSchema } from '/@/components/Table';
import { getTypeArray, getModelArray, filterType, filterModel } from './form.api';
import { h } from 'vue';
import { Icon } from '/@/components/Icon';
export const searchFormSchema: FormSchema[] = [
  {
    field: 'formName',
    label: '表单名称',
    component: 'JInput',
    colProps: { span: 6 },
  },
];
export const formSchema: FormSchema[] = [
  {
    field: 'divider-basic',
    component: 'Divider',
    label: '基础信息',
  },
  {
    field: 'seq',
    label: '序号',
    component: 'InputNumber',
    defaultValue: 1,
    required: true,
    componentProps: {
      placeholder: '请输入序号',
      style: { width: '100%' },
    },
  },
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'formCode',
    label: '表单编码',
    component: 'Input',
    required: true,
  },
  {
    field: 'formName',
    label: '表单名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'icon',
    label: '表单图标',
    component: 'IconPicker',
    required: true,
  },
  {
    field: 'typeId',
    label: '业务类型',
    component: 'JSelectInput',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        placeholder: '请选择业务类型',
        options: getTypeArray(),
        //是否为搜索模式
        showSearch: false,
        onChange: (e: ChangeEvent) => {
          formModel.typeName = filterType(e);
        },
      };
    },
  },
  {
    field: 'typeName',
    label: '业务类型',
    component: 'Input',
    show: false,
  },
  {
    field: 'divider-basic',
    component: 'Divider',
    label: '流程配置',
  },
  {
    field: 'modelKey',
    label: '流程模型',
    component: 'JSelectInput',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        placeholder: '请选流程模型',
        options: getModelArray(),
        //是否为搜索模式
        showSearch: false,
        onChange: (e: ChangeEvent) => {
          formModel.modelName = filterModel(e);
        },
      };
    },
  },
  {
    field: 'modelName',
    label: '流程编码',
    component: 'Input',
    show: false,
  },
  {
    field: 'divider-basic',
    component: 'Divider',
    label: '数据库表配置',
  },
  {
    field: 'tableName',
    label: '数据库表',
    component: 'Input',
    required: true,
  },
  {
    field: 'tableKey',
    label: '主键字段',
    component: 'Input',
    required: true,
  },
  {
    field: 'stateCode',
    label: '状态字段',
    component: 'Input',
    required: true,
  },
];
export const columns: BasicColumn[] = [
  {
    title: '表单编码',
    dataIndex: 'formCode',
  },
  {
    title: '表单名称',
    dataIndex: 'formName',
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 80,
    customRender: ({ record }) => {
      return h(Icon, { icon: record.icon });
    },
  },
  {
    title: '业务类型',
    dataIndex: 'typeName',
  },
  {
    title: '流程模型',
    dataIndex: 'modelName',
  },
  {
    title: '数据库表',
    dataIndex: 'tableName',
  },
  {
    title: '主键字段',
    dataIndex: 'tableKey',
  },
  {
    title: '状态字段',
    dataIndex: 'stateCode',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
  },
];
