export default (key: string, name: string, type?: string): string => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions
	xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
	xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
	xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
	xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
	xmlns:modeler="http://camunda.org/schema/modeler/1.0"
	id="Definitions_${key}"
	targetNamespace="http://bpmn.io/schema/bpmn"
	exporter="Camunda Modeler"
	exporterVersion="5.7.0"
	modeler:executionPlatform="Camunda Platform"
	modeler:executionPlatformVersion="7.18.0">
  <bpmn:process id="${key}" name="${name}" isExecutable="true" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="${key}" />
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;
};
