<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="40%">
    <BasicForm @register="registerForm" :disabled="isDisabled" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { FormSchema } from '/@/components/Table';
  import { saveOrUpdate, getDataById } from '../module.api';
  // 声明Emits
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);

  //自定义接受参数
  const props = defineProps({
    //是否禁用页面
    isDisabled: {
      type: Boolean,
      default: false,
    },
    types: {
      type: Array,
      default: [],
    },
  });
  const formSchema: FormSchema[] = reactive([
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false,
    },
    {
      field: 'seq',
      label: '序号',
      component: 'InputNumber',
      defaultValue: 1,
      required: true,
      componentProps: {
        placeholder: '请输入序号',
        style: { width: '100%' },
      },
    },
    {
      field: 'modelKey',
      label: '模型编码',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入模型编码',
      },
    },
    {
      field: 'modelName',
      label: '模型名称',
      component: 'Input',
      required: true,
      componentProps: {
        placeholder: '请输入模型名字',
      },
    },
    {
      field: 'typeId',
      label: '业务类型',
      component: 'JSearchSelect',
      required: true,
      componentProps: {
        placeholder: '请选择业务类型',
        dictOptions: props.types,
      },
    },
    {
      field: 'remark',
      label: '流程描述',
      component: 'InputTextArea',
      required: true,
      componentProps: {
        placeholder: '请输入流程描述',
      },
    },
  ]);
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    //labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showOkBtn: !props.isDisabled });
    isUpdate.value = !!data?.isUpdate;
    if (unref(isUpdate)) {
      //获取详情
      data.record = await getDataById({ id: data.record.id });
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : '编辑'));
  //表单提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success', values);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
