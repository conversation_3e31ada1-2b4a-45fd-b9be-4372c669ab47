@import "bpmn-js-properties-panel/dist/assets/element-templates.css";
@import "bpmn-js-properties-panel/dist/assets/properties-panel.css";

@import "bpmn-js-connectors-extension/dist/connectors-extension.css";
@import "@bpmn-io/element-template-chooser/dist/element-template-chooser.css";

@import "bpmn-js-bpmnlint/dist/assets/css/bpmn-js-bpmnlint.css";

@import "bpmn-js-token-simulation/assets/css/bpmn-js-token-simulation.css";

@import "bpmn-override";
@import "toolbar";
@import "designer";
@import "palette";
@import "context-pad";
@import "panel";
@import "camunda-penal";
@import "setting";
@import "contextmenu";

#bpmn-design,
.designer-container {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  &.designer-with-bg > .main-content .designer {
    //background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+')
    //  repeat !important;
  }
  &.designer-with-image > .main-content .designer {
    background-repeat: no-repeat !important;
    // background-image: url("/04.jpg") !important;
    background-size: cover;
  }
  .main-content {
    width: 100%;
    flex: 1;
    overflow: hidden;
    display: flex;
    .designer {
      flex: 1;
    }
  }
}
