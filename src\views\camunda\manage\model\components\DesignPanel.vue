<template>
  <div id="bpmn-design">
    <NConfigProvider abstract :hljs="hljs">
      <NDialogProvider>
        <div :class="computedClasses" id="designer-container">
          <NMessageProvider>
            <Toolbar :modelId="modelId"></Toolbar>
            <div class="main-content">
              <Designer :xml.sync="xmlData"></Designer>
              <Panel></Panel>
            </div>
            <ContextMenu></ContextMenu>
          </NMessageProvider>
        </div>
      </NDialogProvider>
    </NConfigProvider>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue';
  import Toolbar from '/@/bpmn/components/Toolbar';
  import Designer from '/@/bpmn/components/Designer';
  import Panel from '/@/bpmn/components/Panel/index.vue';
  import ContextMenu from '/@/bpmn/components/ContextMenu/index.vue';
  import { EditorSettings } from '/@/bpmn/types/editor/settings';
  import { defaultSettings } from '/@/bpmn/config';

  import hljs from 'highlight.js/lib/core';
  import xml from 'highlight.js/lib/languages/xml';
  import json from 'highlight.js/lib/languages/json';
  import { NConfigProvider, NDialogProvider, NMessageProvider } from 'naive-ui';

  hljs.registerLanguage('xml', xml);
  hljs.registerLanguage('json', json);

  //接收参数
  defineProps({
    xmlData: { type: String, required: true },
    modelId: { type: String, required: true },
  });

  const editorSettings = ref<EditorSettings>({ ...defaultSettings });
  const customPalette = computed<boolean>(() => editorSettings.value.paletteMode === 'custom');
  const customPenal = computed<boolean>(() => editorSettings.value.penalMode === 'custom');

  const computedClasses = computed<string>(() => {
    const baseClass = ['designer-container'];
    customPalette.value && baseClass.push('designer-with-palette');
    customPenal.value && baseClass.push('designer-with-penal');
    editorSettings.value.bg === 'grid-image' && baseClass.push('designer-with-bg');
    editorSettings.value.bg === 'image' && baseClass.push('designer-with-image');

    return baseClass.join(' ');
  });
  onMounted(() => {
    document.body.addEventListener('contextmenu', function (ev) {
      ev.preventDefault();
    });
  });
</script>

<style>
  a.bjs-powered-by {
    display: none;
  }
</style>
