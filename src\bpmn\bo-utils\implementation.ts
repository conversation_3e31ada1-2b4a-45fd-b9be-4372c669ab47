import modeler from '/@/bpmn/store/modeler';
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

export const implAttributes = ['class', 'expression', 'delegateExpression', 'type', 'topic'];
export const externalAttributes = ['type', 'topic'];

export function updateImpl(element: any, type: any, value: any) {
  let changeElement: any = {};
  let result: any[] = [];
  if (type != 'external') {
    changeElement[type] = value;
    let newAttrs: any[] = [];
    newAttrs.push(type);
    result = implAttributes.filter((item) => !newAttrs.includes(item));
  } else {
    changeElement.type = type;
    changeElement.topic = value;
    result = implAttributes.filter((item) => !externalAttributes.includes(item));
  }
  result.forEach((item) => {
    changeElement[item] = undefined;
  });
  const modeling = modeler().getModeling as any;
  modeling.updateProperties(element, changeElement);
}

export function getImplObj(element: any) {
  const businessObject = getBusinessObject(element);
  let result: any = {};
  //外部任务
  if (businessObject.hasOwnProperty(externalAttributes[0])) {
    result.type = 'external';
    result.value = businessObject[externalAttributes[1]];
  } else {
    implAttributes.forEach((item) => {
      if (businessObject.hasOwnProperty(item)) {
        result.type = item;
        result.value = businessObject[item];
      }
    });
  }
  return result;
}
