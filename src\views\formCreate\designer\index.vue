<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #tableTitle>
        <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'operation'">
          <a @click="handleDesign(record)"><a-icon type="codepen" style="margin-right: 3px" />设计</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)"><a-icon type="edit" style="margin-right: 3px" />编辑</a>
          <a-divider type="vertical" />
          <a-popconfirm title="是否删除？" ok-text="是" cancel-text="否" @confirm="handleDelete(record)">
            <a><DeleteOutlined style="margin-right: 3px; color: #eb2f96" /><span style="color: #eb2f96">删除</span></a>
          </a-popconfirm>
        </template>
      </template>
    </BasicTable>
    <FormModal @register="registerModal" @success="reload" :isDisabled="isDisabled" />
  </div>
</template>

<script setup lang="ts" name="camunda-type">
  import { ref, unref, watch, onMounted } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import FormModal from './components/FormModal.vue';
  import { getList, deleteForm, loadType, loadModel } from './form.api';
  import { columns, searchFormSchema } from './form.data';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import { getToken } from '/@/utils/auth';
  import { useGlobSetting } from '@/hooks/setting';
  const globSetting = useGlobSetting();

  const [registerModal, { openModal }] = useModal();
  const isDisabled = ref(false);

  const [registerTable, { reload, setProps }] = useTable({
    title: '业务表单',
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    //自定义默认排序
    defSort: {
      column: 'seq',
      order: 'asc',
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: true,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
  });

  /**
   * 页面配置
   */
  function handleDesign(record) {
    // 新标签页打开
    window.open(`${globSetting.amisUrl}#/edit/form/${record.id}/${getToken()}`, '_blank');
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    isDisabled.value = false;
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    isDisabled.value = false;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteForm({ id: record.id }, reload);
  }

  onMounted(() => {
    //获取业务类型数组
    loadType();
    //获取模型数组
    loadModel();
  });

  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less" scoped></style>
