<template>
  <n-button-group>
    <n-button ghost size="small" @click="switchCheckStrictly(1)"> 父子关联 </n-button>
    <n-button ghost size="small" @click="switchCheckStrictly(2)"> 取消关联 </n-button>
    <n-button ghost size="small" @click="checkALL"> 全部勾选 </n-button>
    <n-button ghost size="small" @click="cancelCheckALL"> 取消勾选 </n-button>
    <n-button ghost size="small" @click="expandAll"> 展开所有 </n-button>
    <n-button ghost size="small" @click="closeAll"> 合并所有 </n-button>
  </n-button-group>
  <a-card :title="title" size="small">
    <a-tree
      checkable
      :treeData="treeData"
      :checkStrictly="checkStrictly"
      @check="onCheck"
      @select="onSelect"
      @expand="onExpand"
      :autoExpandParent="autoExpandParent"
      :expandedKeys="expandedKeys"
      :checkedKeys="checkedKeys"
      :style="{ height: treeHeight + 'px', overflowY: 'scroll' }"
    >
    </a-tree>
  </a-card>
</template>

<script>
  import { defineComponent } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  export default defineComponent({
    props: {
      departList: {
        type: Array,
        required: false,
        default: () => [],
      },
    },
    data() {
      return {
        url: {
          queryTreeList: '/sys/sysDepart/queryTreeList',
        },
        treeHeight: 500,
        loading: false,
        treeData: [],
        autoExpandParent: true,
        expandedKeys: [],
        dataList: [],
        checkedKeys: [],
        allTreeKeys: [],
        checkedRows: [],
        searchValue: '',
        checkStrictly: true,
        multi: true,
      };
    },
    methods: {
      refresh() {
        this.setSelOrgIds();
      },
      loadDepart() {
        // 这个方法是找到所有的部门信息
        var that = this;
        //所有的树信息
        that.treeData = [];
        that.loading = false;
        let params = {};
        defHttp.get({ url: this.url.queryTreeList, params }, { isTransformResponse: false }).then((res) => {
          if (res.success) {
            for (let i = 0; i < res.result.length; i++) {
              let temp = res.result[i];
              that.treeData.push(temp);
              that.getAllKeys(temp);
            }
            that.generateList(that.treeData);
            this.loading = true;
          }
        });
      },
      generateList(data) {
        for (let i = 0; i < data.length; i++) {
          const node = data[i];
          const key = node.key;
          const title = node.title;
          this.dataList.push({ key, title });
          if (node.children) {
            this.generateList(node.children);
          }
        }
      },
      //获取所有节点
      getAllKeys(node) {
        this.allTreeKeys.push(node.key);
        if (node.children && node.children.length > 0) {
          for (let a = 0; a < node.children.length; a++) {
            this.getAllKeys(node.children[a]);
          }
        }
      },
      onCheck(checkedKeys) {
        if (!this.multi) {
          let arr = checkedKeys.checked.filter((item) => this.checkedKeys.indexOf(item) < 0);
          this.checkedKeys = [...arr];
        } else {
          if (this.checkStrictly) {
            this.checkedKeys = checkedKeys.checked;
          } else {
            this.checkedKeys = checkedKeys;
          }
        }
      },
      onSelect(selectedKeys, info) {
        //取消关联的情况下才走onSelect的逻辑
        if (this.checkStrictly) {
          let keys = [];
          keys.push(selectedKeys[0]);
          if (!this.checkedKeys || this.checkedKeys.length === 0 || !this.multi) {
            this.checkedKeys = [...keys];
            this.checkedRows = [info.node.dataRef];
          } else {
            let currKey = info.node.dataRef.key;
            if (this.checkedKeys.indexOf(currKey) >= 0) {
              this.checkedKeys = this.checkedKeys.filter((item) => item !== currKey);
            } else {
              this.checkedKeys.push(...keys);
            }
          }
        }
      },
      onExpand(expandedKeys) {
        this.expandedKeys = expandedKeys;
        this.autoExpandParent = false;
      },
      switchCheckStrictly(v) {
        if (v == 1) {
          this.checkStrictly = false;
        } else if (v == 2) {
          this.checkStrictly = true;
        }
      },
      checkALL() {
        this.checkedKeys = this.allTreeKeys;
      },
      cancelCheckALL() {
        this.checkedKeys = [];
      },
      //展开所有节点
      expandAll() {
        this.expandedKeys = this.allTreeKeys;
      },
      //收缩所有节点
      closeAll() {
        this.expandedKeys = [];
      },
      setSelOrgIds() {
        let self = this;
        this.checkedKeys = [];
        this.departList.forEach(function (id) {
          self.checkedKeys.push(id);
        });
      },
    },
    mounted() {
      this.refresh();
    },
    created() {
      this.loadDepart();
      this.expandAll();
    },
    computed: {
      title() {
        return '已选单位（' + this.checkedKeys.length + '）';
      },
    },
    watch: {
      departList: function (newValue, oldValue) {
        this.setSelOrgIds();
      },
    },
  });
</script>

<style scoped></style>
