<template>
  <div class="checkinfo-container">
    <div :style="{ width: pageWidth + 'px' }">
      <div style="display: block">
        <a-card>
          <div class="checkLabel">流程名称：</div>
          <div style="margin-right: 100px" class="checkText">{{ procDefName }}</div>
          <div class="checkLabel">发起人:</div>
          <div class="checkText">{{ startUserName }}</div>
        </a-card>
      </div>
      <div style="display: block">
        <div class="flow-container">
          <a-tabs type="card">
            <a-tab-pane v-if="allowOperate" key="1" tab="节点审批"
              ><CheckForm
                :procInstId="procInstId"
                :allowOperate="allowOperate"
                :taskId="taskId"
                :businessKey="businessKey"
                :procDefKey="procDefKey"
                :procDefName="procDefName"
                @submitTask="closeModal"
              >
              </CheckForm
            ></a-tab-pane>
            <a-tab-pane key="2" tab="进度查看">
              <CheckProgress :procInstId="procInstId"></CheckProgress>
            </a-tab-pane>
            <a-tab-pane key="3" tab="操作记录">
              <OpLogs :procInstId="procInstId"></OpLogs>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import CheckForm from '@/views/camunda/use/checkInfo/CheckForm.vue';
  import CheckProgress from '@/views/camunda/use/checkInfo/Progress.vue';
  import OpLogs from '@/views/camunda/use/checkInfo/OpLogs.vue';
  // 声明Emits
  const emit = defineEmits(['close']);
  defineProps({
    pageWidth: {
      type: Number,
      default: 1200,
    },
    startUserName: {
      type: String,
      default: '',
    },
    procDefKey: {
      type: String,
      default: '',
    },
    procDefName: {
      type: String,
      default: '',
    },
    procInstId: {
      type: String,
      default: '',
    },
    taskId: {
      type: String,
      default: '',
    },
    allowOperate: {
      type: Boolean,
      default: false,
    },
    businessKey: {
      type: String,
      default: '',
    },
  });
  function closeModal() {
    emit('close');
  }
</script>

<style>
  .checkText {
    line-height: 34px;
    display: inline-block;
    font-family: 'Microsoft Yahei', sans-serif;
    font-weight: bold;
    font-size: 16px;
    color: #808080;
  }
  .checkLabel {
    line-height: 34px;
    display: inline-block;
    font-family: 'Microsoft Yahei', sans-serif;
    font-weight: bold;
    font-size: 18px;
    color: #595959;
    margin-right: 15px;
  }
  .checkinfo-container {
    display: flex; /* 设置容器为 flexbox 布局 */
    justify-content: center; /* 水平居中 */
  }
  .flow-container {
    background: #f5f5f5;
    overflow: hidden;
    padding: 24px;
  }
  .flow-container > :where(.css-dev-only-do-not-override-udyjmm).ant-tabs-top > .ant-tabs-nav,
  :where(.css-dev-only-do-not-override-udyjmm).ant-tabs-bottom > .ant-tabs-nav,
  :where(.css-dev-only-do-not-override-udyjmm).ant-tabs-top > div > .ant-tabs-nav,
  :where(.css-dev-only-do-not-override-udyjmm).ant-tabs-bottom > div > .ant-tabs-nav {
    margin: 0 0 0 0;
  }
</style>
