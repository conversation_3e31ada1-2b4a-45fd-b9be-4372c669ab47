<template>
  <n-collapse-item name="element-implementation">
    <template #header>
      <collapse-title title="业务接口">
        <lucide-icon name="Plug" />
      </collapse-title>
      <div style="margin-left: 3px; color: red">*</div>
    </template>
    <a-descriptions bordered size="small">
      <a-descriptions-item :span="3" label="类型" style="width: 50%"> {{ transName }} </a-descriptions-item>
      <a-descriptions-item :span="3" label="方法" style="width: 50%"> {{ oldProperty.value }} </a-descriptions-item>
    </a-descriptions>
    <a-button type="primary" preIcon="ant-design:setting-outlined" class="inline-large-button" @click="openPropertyModel()"> 业务接口配置 </a-button>
    <a-modal v-model:open="modelVisible" centered title="业务接口配置" :style="{ width: '640px' }">
      <a-card>
        <n-form ref="formRef" label-placement="left" :model="newProperty" :rules="rules" aria-modal="true" label-width="100px">
          <a-row>
            <a-col :span="24">
              <n-form-item label="实现类型" path="type">
                <n-select v-model:value="newProperty.type" :options="implTypeOptions" placeholder="请选择实现类型" />
              </n-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <n-form-item label="接口方法" path="value">
                <n-input v-model:value="newProperty.value" @keydown.enter.prevent placeholder="请输入接口方法" />
              </n-form-item>
            </a-col>
          </a-row>
        </n-form>
      </a-card>
      <template #footer>
        <a-button key="back" @click="modelVisible = false">取消</a-button>
        <a-button key="submit" type="primary" @click="updateProperty">确认</a-button>
      </template>
    </a-modal>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import { updateImpl, getImplObj } from '/@/bpmn/bo-utils/implementation';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import { mapState } from 'pinia';
  import modelerStore from '/@/bpmn/store/modeler';
  import { FormInst } from 'naive-ui';
  export default defineComponent({
    name: 'ElementImplementation',
    data() {
      return {
        transName: '',
        oldProperty: { type: '', value: '' },
        newProperty: { type: '', value: '' },
        modelVisible: false,
        implTypeOptions: [
          { label: 'Java Class', value: 'class' },
          { label: 'Expression', value: 'expression' },
          { label: 'DelegateExpression', value: 'delegateExpression' },
          { label: 'External', value: 'external' },
        ],
        rules: {
          type: { level: 'warning', required: true, message: '实现类型不能为空', trigger: ['blur', 'change'] },
          value: { level: 'warning', required: true, message: '实现方法不能为空', trigger: ['blur', 'change'] },
        },
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      async reloadExtensionProperties() {
        this.modelVisible = false;
        await this.$nextTick();
        this.oldProperty = getImplObj(this.getActive);
        if (this.oldProperty.type != '' && this.oldProperty.type != null) {
          const findImpl: any = this.implTypeOptions.find((implType) => implType.value === this.oldProperty.type);
          this.transName = findImpl.label;
        } else {
          this.transName = '';
        }
      },
      updateiImplType() {
        this.newProperty.value = '';
      },
      async openPropertyModel() {
        this.modelVisible = true;
        await this.$nextTick();
        (this.$refs.formRef as FormInst).restoreValidation();
        this.newProperty = Object.assign({}, this.oldProperty);
      },
      async updateProperty() {
        (this.$refs.formRef as FormInst).validate(async (_errors, extra) => {
          if (!extra.warnings) {
            updateImpl(this.getActive, this.newProperty.type, this.newProperty.value);
          }
        });
      },
    },
  });
</script>
