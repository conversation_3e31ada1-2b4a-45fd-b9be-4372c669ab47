import modeler from '/@/bpmn/store/modeler';

/**
 * 判断是否是bpmn:Process
 * @param element
 * @returns
 */
export function isProcessElement(element) {
  const type = 'bpmn:Process';
  return element.type == type;
}
/**
 * 判断是否是多实例的用户任务
 * @param element
 * @returns
 */
export function isMultiUserTask(element) {
  const type = 'bpmn:UserTask';
  if (element.type == type) {
    const businessObject = element.businessObject;
    if (businessObject.hasOwnProperty('loopCharacteristics')) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}
/**
 * 判断是否是业务任务
 * @param element
 * @returns
 */
export function isServiceTask(element) {
  const type = 'bpmn:ServiceTask';
  if (element.type == type) {
    return true;
  } else {
    return false;
  }
}
