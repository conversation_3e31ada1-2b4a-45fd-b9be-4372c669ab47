.setting {
  width: 560px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  transition: all cubic-bezier(0.8, 0.4, 0.8, 0.4) 0.24s;
  transform: translateX(100%);
  z-index: 101;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.2);
  &.setting_open {
    transform: translateX(0);
    box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.2);
    .toggle-button {
      transform: translateX(0);
      z-index: -1;
    }
  }
  .setting-container {
    width: 100%;
    height: 100%;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
    .setting-header {
      width: 100%;
      height: 48px;
      border-bottom: 1px solid #999999;
      padding: 0 18px;
      font-size: 20px;
      line-height: 48px;
    }
    .setting-content {
      flex: 1;
      padding: 16px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .setting-footer {
      width: 100%;
      height: 48px;
      border-top: 1px solid #999999;
      padding: 0 18px;
    }
  }
  .toggle-button {
    width: 56px;
    height: 56px;
    box-sizing: border-box;
    padding: 8px;
    position: absolute;
    left: 0;
    bottom: 20vh;
    z-index: 0;
    background: rgba(64, 158, 255, 1);
    border-radius: 8px;
    transform: translateX(-100%);
    cursor: pointer;
    transition: all cubic-bezier(0.8, 0.4, 0.8, 0.4) 0.24s;
  }
}
.settings-model {
  width: 40vw;
  min-width: 480px;
  height: 80vh;
  overflow: hidden;
}

.tips-message {
  width: 100%;
  text-align: left;
  color: #303133;
  display: flex;
  .grip-tips {
    display: grid;
    p:first-child {
      grid-row-start: 1;
      grid-row-end: 5;
    }
    +.grip-tips {
      margin-left: 20px;
    }
  }
  p {
    margin: 0 0 4px 0;
    font-weight: bold;
  }
}
// .sponsorship-image {
//   width: 150px;
//   height: 150px;
//   background-size: 140% 140%;
//   background-position: center center;
//   display: inline-block;
//   &.wechat {
//     background-image: url('./wechat.jpg');
//   }
//   &.alipay {
//     margin-left: 8px;
//     background-image: url('./alipay.png');
//   }
// }

.n-form-item.theme-list .n-form-item-blank {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  .theme-item {
    width: 100%;
    display: flex;
    align-items: center;
  }
  .theme-item_label {
    width: 280px;
    text-align: right;
  }
  .n-color-picker,
  .n-input-number {
    width: auto;
    flex: 1;
  }
}
.theme-item + .theme-item {
  margin-top: 8px;
}
