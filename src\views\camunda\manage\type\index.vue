<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #tableTitle>
        <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">新增</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getActions(record)" />
      </template>
      <template #bodyCell="{ column, text }">
        <template v-if="column.key === 'state'">
          <span>
            <a-tag v-if="text == 'ACTIVE'" color="green">激活</a-tag>
            <a-tag v-if="text == 'SUSPEND'" color="pink">停用</a-tag>
          </span>
        </template>
      </template>
    </BasicTable>
    <ModuleModal @register="registerModal" @success="reload" :isDisabled="isDisabled" />
  </div>
</template>

<script setup lang="ts" name="camunda-type">
  import { ref, unref, watch } from 'vue';
  //列表组件
  import { ActionItem, BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import ModuleModal from './components/ModuleModal.vue';
  import { getList, deleteData } from './module.api';
  import { columns, searchFormSchema } from './module.data';
  const [registerModal, { openModal }] = useModal();
  const isDisabled = ref(false);

  const [registerTable, { reload, setProps }] = useTable({
    title: '业务类型',
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    //自定义默认排序
    defSort: {
      column: 'seq',
      order: 'asc',
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: true,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: false,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  /**
   * 操作列定义
   * @param record
   */
  function getActions(record): ActionItem[] {
    return [
      {
        label: '详情',
        icon: 'ant-design:file-search-outlined',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '编辑',
        icon: 'clarity:note-edit-line',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    isDisabled.value = false;
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    isDisabled.value = false;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 详情页面
   */
  function handleDetail(record) {
    isDisabled.value = true;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteData({ id: record.id }, reload);
  }

  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less" scoped></style>
