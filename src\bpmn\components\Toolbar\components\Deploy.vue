<template>
  <NButtonGroup>
    <NPopover trigger="hover">
      <template #trigger>
        <NButton @click="deploy"> <LucideIcon name="Play" :size="16"></LucideIcon> </NButton>
      </template>
      <span>发布</span>
    </NPopover>
  </NButtonGroup>
</template>

<script setup lang="ts">
  import { NButton, NButtonGroup, NPopover } from 'naive-ui';
  import LucideIcon from '/@/bpmn/components/common/LucideIcon.vue';
  import { Modal } from 'ant-design-vue';
  import { defHttp } from '/@/utils/http/axios';
  import modeler from '/@/bpmn/store/modeler';
  //接收参数
  const props = defineProps({
    modelId: { type: String, required: true },
  });
  enum Api {
    deploy = '/act/manage/deploy',
  }
  const modelerStore = modeler();
  //发布
  function deploy() {
    Modal.confirm({
      title: '发布当前流程',
      content: '是否确认保存并发布此版本？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        submit();
      },
    });
  }
  //提交发布
  const submit = async () => {
    try {
      const modeler = modelerStore.getModeler!;
      if (!modeler) {
        return window.__messageBox.warning('模型加载失败，请刷新重试');
      }
      const { xml } = await modeler.saveXML({ format: true, preamble: true });
      const params = {
        modelId: props.modelId,
        modelContent: xml,
      };
      //提交表单
      defHttp.post({ url: Api.deploy, params: params }, { isTransformResponse: false }).then((res) => {
        if (res.success) {
          window.__messageBox.success('发布成功！');
        } else {
          window.__messageBox.success(res.message);
        }
      });
    } catch (e) {
      window.__messageBox.error((e as Error).message || (e as string));
    }
  };
</script>
<style scoped></style>
