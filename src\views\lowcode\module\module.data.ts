import { BasicColumn, FormSchema } from '/@/components/Table';

export const searchFormSchema: FormSchema[] = [
  {
    field: 'moduleName',
    label: '业务模块',
    component: 'JInput',
    colProps: { span: 6 },
  },
];
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'moduleName',
    label: '业务模块',
    component: 'Input',
    required: true,
  },
  {
    field: 'description',
    label: '模块描述',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'seq',
    label: '序号',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入序号',
      style: { width: '100%' },
    },
  },
  {
    field: 'delFlag',
    label: '删除状态',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: 'createBy',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: 'createTime',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: 'updateBy',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: 'updateTime',
    component: 'Input',
    show: false,
  },
];
export const columns: BasicColumn[] = [
  {
    title: '业务模块',
    dataIndex: 'moduleName',
  },
  {
    title: '模块描述',
    dataIndex: 'description',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '更新人',
    dataIndex: 'updateBy',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 280,
  },
];
