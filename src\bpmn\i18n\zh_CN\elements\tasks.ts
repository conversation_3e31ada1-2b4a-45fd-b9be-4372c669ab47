export default {
  Task: '任务',
  'Send Task': '发送任务',
  'Receive Task': '接收任务',
  'User Task': '用户任务',
  'Manual Task': '手工任务',
  'Business Rule Task': '业务规则任务',
  'Service Task': '服务任务',
  'Script Task': '脚本任务',
  'Call Activity': '调用活动',
  'Sub Process (collapsed)': '子流程（折叠的）',
  'Sub Process (expanded)': '子流程（展开的）',
  'Start Event': '开始事件',
  'Intermediate Throw Event': '中间事件',
  'End Event': '结束事件',
  StartEvent: '开始事件',
  EndEvent: '结束事件',
  SendTask: '发送任务',
  ReceiveTask: '接收任务',
  UserTask: '用户任务',
  ManualTask: '手工任务',
  BusinessRuleTask: '业务规则任务',
  ServiceTask: '服务任务',
  ScriptTask: '脚本任务',
  CallActivity: '调用活动',
  SubProcess: '子流程'
}
