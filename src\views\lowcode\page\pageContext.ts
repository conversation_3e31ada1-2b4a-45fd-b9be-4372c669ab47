import type { InjectionKey } from 'vue';
import type { Emitter } from '/@/utils/mitt';
import { createContext, useContext } from '/@/hooks/core/useContext';

export interface PageContextProps {
  pageEmitter: Emitter;
}

const key: InjectionKey<PageContextProps> = Symbol();

export function createPageContext(context: PageContextProps) {
  return createContext<PageContextProps>(context, key, { readonly: false, native: true });
}

export function usePageContext() {
  return useContext<PageContextProps>(key);
}
