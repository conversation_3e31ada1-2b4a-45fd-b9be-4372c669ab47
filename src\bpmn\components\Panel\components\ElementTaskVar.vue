<template>
  <n-collapse-item name="element-task-var">
    <template #header>
      <collapse-title title="流转参数">
        <lucide-icon name="Code" />
      </collapse-title>
    </template>
    <template #header-extra>
      <n-tag type="primary" round>
        {{ varArray.length }}
      </n-tag>
    </template>
    <a-empty v-show="varArray.length == 0" />
    <a-descriptions bordered size="small" v-show="varArray.length > 0">
      <a-descriptions-item v-for="(item, index) in varArray" :key="index" :label="item" :span="3">
        <a @click="delVar(item)"> <a-icon type="delete" style="margin-right: 3px; color: red" /><span style="color: red">移除</span></a>
      </a-descriptions-item>
    </a-descriptions>
    <a-button type="primary" preIcon="ant-design:plus-outlined" class="inline-large-button" @click="openPropertyModel()"> 添加流转参数 </a-button>
    <a-modal v-model:open="modelVisible" centered title="添加流转参数" :width="dialogWidth">
      <a-card>
        <n-form ref="formRef" label-placement="left" :model="newProperty" :rules="rules" aria-modal="true" label-width="100px">
          <a-row>
            <a-col :span="24">
              <n-form-item label="参数名称" path="name">
                <n-input v-model:value="newProperty.name" @keydown.enter.prevent placeholder="请输入参数名称" />
              </n-form-item>
            </a-col>
          </a-row>
        </n-form>
      </a-card>
      <template #footer>
        <a-button key="back" @click="modelVisible = false">取消</a-button>
        <a-button key="submit" type="primary" @click="submit">确认</a-button>
      </template>
    </a-modal>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent, markRaw } from 'vue';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import { mapState } from 'pinia';
  import modelerStore from '/@/bpmn/store/modeler';
  import { addExtensionProperty, getExtensionProperties, removeExtensionProperty } from '/@/bpmn/bo-utils/extensionPropertiesUtil';
  import { FormInst, FormRules } from 'naive-ui';
  export default defineComponent({
    name: 'ElementTaskVar',
    data() {
      return {
        propertyTaskName: 'proc_cus_task_vars',
        extensionsRaw: [],
        extensions: [],
        varArray: [] as any[],
        modelVisible: false,
        rules: {
          name: { level: 'warning', required: true, message: '参数名称不能为空', trigger: ['blur', 'change'] },
        } as FormRules,
        newProperty: {} as any,
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
      dialogWidth() {
        return '650px';
      },
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      delVar(item) {
        const index = this.varArray.indexOf(item);
        if (index > -1) {
          this.varArray.splice(index, 1);
        }
        this.updateProperty(this.varArray);
      },
      async reloadExtensionProperties() {
        let self = this;
        this.modelVisible = false;
        await this.$nextTick();
        //初始化
        this.varArray = [];
        //加载数据
        (this.extensionsRaw as any[]) = markRaw(getExtensionProperties(this.getActive));
        this.extensions = JSON.parse(JSON.stringify(this.extensionsRaw));
        if (this.extensions.length > 0) {
          const extension: any = this.extensions.find((extension) => extension['name'] === self.propertyTaskName);
          if (extension != null) {
            if (extension['value'] != null && extension['value'].length > 0) {
              this.varArray = extension['value'].split(',');
            }
          } else {
            this.varArray = [];
          }
        }
      },

      async openPropertyModel() {
        this.reloadExtensionProperties();
        this.modelVisible = true;
        await this.$nextTick();
        this.newProperty = { name: '' };
      },
      //提交
      async submit() {
        let self = this;
        (this.$refs.formRef as FormInst).validate(async (_errors, extra) => {
          if (!extra.warnings) {
            const name = self.newProperty.name;
            let newArr = self.varArray;
            newArr.push(name);
            self.updateProperty(newArr);
          }
        });
      },
      async updateProperty(arr) {
        let taskProperty = { name: this.propertyTaskName };
        taskProperty['value'] = arr.join(',');
        this.removeProperty();
        addExtensionProperty(this.getActive, taskProperty);
      },
      removeProperty() {
        let self = this;
        const propIndex = getExtensionProperties(this.getActive).findIndex((extension) => extension.name === self.propertyTaskName);
        removeExtensionProperty(this.getActive, this.extensionsRaw[propIndex]);
      },
    },
  });
</script>
