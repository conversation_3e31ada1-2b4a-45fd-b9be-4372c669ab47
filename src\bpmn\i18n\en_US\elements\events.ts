export default {
  StartEvent: 'Start Event',
  EndEvent: 'End Event',
  IntermediateThrowEvent: 'IntermediateThrowEvent',
  IntermediateCatchEvent: 'IntermediateCatchEvent',
  'Message Start Event': 'Message Start Event',
  'Timer Start Event': 'Timer Start Event',
  'Conditional Start Event': 'Conditional Start Event',
  'Signal Start Event': 'Signal Start Event',
  'Error Start Event': 'Error Start Event',
  'Escalation Start Event': 'Escalation Start Event',
  'Compensation Start Event': 'Compensation Start Event',
  'Message Start Event (non-interrupting)': 'Message Start Event (non-interrupting)',
  'Timer Start Event (non-interrupting)': 'Timer Start Event (non-interrupting)',
  'Conditional Start Event (non-interrupting)': '条件开始事件（非中断）',
  'Signal Start Event (non-interrupting)': '信号开始事件（非中断）',
  'Escalation Start Event (non-interrupting)': '升级开始事件（非中断）',
  'Message Intermediate Catch Event': '消息中间捕获事件',
  'Message Intermediate Throw Event': '消息中间抛出事件',
  'Timer Intermediate Catch Event': '定时中间捕获事件',
  'Escalation Intermediate Throw Event': '升级中间抛出事件',
  'Conditional Intermediate Catch Event': '条件中间捕获事件',
  'Link Intermediate Catch Event': '链接中间捕获事件',
  'Link Intermediate Throw Event': '链接中间抛出事件',
  'Compensation Intermediate Throw Event': '补偿中间抛出事件',
  'Signal Intermediate Catch Event': '信号中间捕获事件',
  'Signal Intermediate Throw Event': '信号中间抛出事件',
  'Message End Event': '消息结束事件',
  'Escalation End Event': '定时结束事件',
  'Error End Event': '错误结束事件',
  'Cancel End Event': '取消结束事件',
  'Compensation End Event': '补偿结束事件',
  'Signal End Event': '信号结束事件',
  'Terminate End Event': '终止结束事件',
  'Message Boundary Event': '消息边界事件',
  'Message Boundary Event (non-interrupting)': '消息边界事件（非中断）',
  'Timer Boundary Event': '定时边界事件',
  'Timer Boundary Event (non-interrupting)': '定时边界事件（非中断）',
  'Escalation Boundary Event': '升级边界事件',
  'Escalation Boundary Event (non-interrupting)': '升级边界事件（非中断）',
  'Conditional Boundary Event': '条件边界事件',
  'Conditional Boundary Event (non-interrupting)': '条件边界事件（非中断）',
  'Error Boundary Event': '错误边界事件',
  'Cancel Boundary Event': 'Cancel Boundary Event',
  'Signal Boundary Event': 'Signal Boundary Event',
  'Signal Boundary Event (non-interrupting)': 'Signal Boundary Event (non-interrupting)',
  'Compensation Boundary Event': '补偿边界事件'
}
