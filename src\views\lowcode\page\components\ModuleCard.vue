<template>
  <PageWrapper class="list-card" title="业务模块">
    <div class="list-card__content">
      <a-list>
        <a-row :gutter="16">
          <template v-for="item in list" :key="item.title">
            <a-col :span="24">
              <a-list-item>
                <a-card :hoverable="true" :class="['list-card__card', { 'active-card': item.id === activeKey }]" @click="moduleClick(item.id)">
                  <div class="list-card__card-title">
                    <Icon class="icon" icon="logos:vue" color="1890ff" />
                    {{ item.moduleName }}
                  </div>
                  <div class="list-card__card-detail"> {{ item.description }} </div>
                </a-card>
              </a-list-item>
            </a-col>
          </template>
        </a-row>
      </a-list>
    </div>
  </PageWrapper>
</template>
<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue';
  import Icon from '/@/components/Icon/index';
  import { PageWrapper } from '/@/components/Page';
  import { defHttp } from '@/utils/http/axios';
  import { usePageContext } from '../pageContext';

  //获取事件总线
  const { pageEmitter } = usePageContext();

  const list = reactive<any[]>([]);
  const activeKey = ref('');
  /**
   * 加载业务模块列表
   *
   * @returns 无返回值
   */
  const loadModule = async () => {
    const res = await defHttp.get({ url: '/lcd/module/listAll' }, { isTransformResponse: false });
    if (res.success) {
      Object.assign(list, res.result);
    }
  };
  const moduleClick = (key) => {
    if (activeKey.value == key) {
      activeKey.value = '';
    } else {
      activeKey.value = key;
    }
    pageEmitter.emit('on-module-click', activeKey.value);
  };
  onMounted(() => {
    loadModule();
  });
</script>
<style lang="less" scoped>
  /* 深蓝色边框 */
  .active-card {
    border-color: #1890ff !important;
  }
  .list-card {
    &__link {
      margin-top: 10px;
      font-size: 14px;

      a {
        margin-right: 30px;
      }

      span {
        margin-left: 5px;
      }
    }

    &__card {
      width: 100%;
      margin-bottom: -8px;

      .ant-card-body {
        padding: 16px;
      }

      &-title {
        margin-bottom: 5px;
        font-size: 16px;
        font-weight: 500;
        color: @text-color;

        .icon {
          margin-top: -5px;
          margin-right: 10px;
          font-size: 38px !important;
        }
      }

      &-detail {
        padding-top: 10px;
        padding-left: 10px;
        font-size: 14px;
        color: @text-color-secondary;
        display: -webkit-box;
        -webkit-line-clamp: 3; /* 限制显示2行 */
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>
