<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #action="{ record }">
        <a v-if="record.firstTask == 'Y'" @click="editForm(record)">
          <EditOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">工单编辑</span></a
        >
        <a-divider v-if="record.firstTask == 'Y'" type="vertical" />
        <a a-divider v-if="record.firstTask == 'N'" @click="showForm(record)">
          <FileSearchOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">工单明细</span></a
        >
        <a-divider v-if="record.firstTask == 'N'" a-divider type="vertical" />
        <a @click="openCheckInfoModal(record)">
          <FileSearchOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">审批详情</span></a
        >
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'state'">
          <span>
            <a-tag v-if="text == 'ACTIVE' && record.firstTask == 'N'" color="pink">进行中</a-tag>
            <a-tag v-if="text == 'COMPLETED' && record.firstTask == 'N'" color="green">已完成</a-tag>
            <a-tag v-if="record.firstTask == 'Y'" color="#FF4D4F">已退回</a-tag>
          </span>
        </template>
      </template>
    </BasicTable>
    <a-modal
      v-if="open"
      v-model:open="open"
      title="流程窗口"
      @cancel="closeModal"
      :footer="null"
      :keyboard="false"
      width="100%"
      wrap-class-name="flow-modal"
    >
      <CheckInfo
        :allowOperate="checkInfo.allowOperate"
        :startUserName="checkInfo.startUserName"
        :procDefKey="checkInfo.procDefKey"
        :procDefName="checkInfo.procDefName"
        :procInstId="checkInfo.procInstId"
        :businessKey="checkInfo.businessKey"
      ></CheckInfo>
    </a-modal>
    <FormModal @register="registerModal" :title="title" :feat="feat" @close="closeForm" />
  </div>
</template>

<script setup lang="ts" name="oa-order">
  import { reactive, ref, unref, watch } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { getList } from './order.api';
  import { columns, searchFormSchema } from './order.data';
  import { FileSearchOutlined, BranchesOutlined, EditOutlined } from '@ant-design/icons-vue';
  import CheckInfo from '@/views/camunda/use/checkInfo/index.vue';
  import { useModal } from '/@/components/Modal';
  import FormModal from '/@/views/oa/components/FormModal.vue';

  const [registerModal, { openModal }] = useModal();
  //窗口属性
  const title = ref<string>('');
  const feat = ref<string>('View');

  const [registerTable, { reload, setProps }] = useTable({
    title: '业务类型',
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    //自定义默认排序
    defSort: {
      column: 'startTime',
      order: 'desc',
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: true,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 280,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  //窗口开关
  const open = ref(false);
  //窗口参数对象
  const checkInfo = reactive({
    startUserName: '', //发起人
    procDefKey: '', //流程key
    procDefName: '', //流程名称
    procInstId: '', //实例id
    businessKey: '', //业务主键
    allowOperate: false, //是否允许操作
  });
  /**
   * 打开详情窗口
   */
  function openCheckInfoModal(record) {
    checkInfo.businessKey = record.businessKey;
    checkInfo.procInstId = record.procInstId;
    checkInfo.procDefKey = record.procDefKey;
    checkInfo.procDefName = record.procDefName;
    checkInfo.startUserName = record.startUserName;
    open.value = true;
  }
  /**
   * 流程窗口关闭
   * @param record
   */
  function closeModal() {
    open.value = false;
  }
  /**
   * 展示表单窗口
   * @param form
   */
  const showForm = (form) => {
    title.value = form.formName;
    feat.value = 'View';
    openModal(true, { processKey: form.procDefKey, formId: form.formId, businessKey: form.businessKey });
  };
  const closeForm = () => {
    reload();
  };
  /**
   * 编辑修改表单窗口
   * @param form
   */
  const editForm = (form) => {
    title.value = form.formName;
    feat.value = 'Edit';
    openModal(true, { processKey: form.procDefKey, formId: form.formId, businessKey: form.businessKey, taskId: form.taskId });
  };

  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less">
  .flow-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
      padding: 20px;
    }
  }
</style>
