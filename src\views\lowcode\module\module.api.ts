import { defHttp } from '/@/utils/http/axios';

export enum Api {
  getList = '/lcd/module/list',
  save = '/lcd/module/add',
  edit = '/lcd/module/edit',
  delete = '/lcd/module/delete',
  getById = '/lcd/module/queryById',
}
/**
 * 查询表单详情
 * @param params
 */
export const getById = (params) => {
  return defHttp.get({ url: Api.getById, params });
};
/**
 * 查询表单列表
 * @param params
 */
export const getList = (params) => defHttp.get({ url: Api.getList, params });
/**
 * 保存或者更新主类
 */
export const saveOrUpdateProp = (params, isUpdate) => {
  if (isUpdate) {
    return defHttp.post({ url: Api.edit, params });
  } else {
    return defHttp.post({ url: Api.save, params });
  }
};
/**
 * 删除模型表单
 * @param params
 */
export const deleteModule = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
