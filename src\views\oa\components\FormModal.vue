<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="title"
    :footer="null"
    :defaultFullscreen="false"
    width="60%"
    :destroyOnClose="true"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <!-- 动态渲染：对象非空时才显示组件 -->
    <AmisRenderer ref="amisRenderer" v-if="!isSchemaEmpty" :schema="schema" :env="env" />
    <!-- 可选：显示空状态提示 -->
    <div v-else class="empty-container">
      <a-empty
        :image="netWorkSvg"
        :image-style="{
          height: '240px',
        }"
      >
        <template #description>
          <span> {{ errorText }} </span>
        </template>
      </a-empty>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { AmisRenderer } from '/@/components/AmisRenderer';
  import netWorkSvg from '/@/assets/svg/net-error.svg';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';
  import { defHttp } from '/@/utils/http/axios';

  const emit = defineEmits(['close']);
  //自定义接受参数
  const props = defineProps({
    //是否禁用页面
    title: {
      type: String,
    },
    feat: {
      type: String,
    },
  });
  const amisRenderer = ref();
  // 定义schema对象来存储页面结构数据
  const schema = reactive({});
  //顶层数据域的值，用于传递给amis组件
  const glob = useGlobSetting();
  const userStore = useUserStore();
  const onEvent = reactive({
    submitSucc: {
      weight: 0,
      actions: [
        {
          ignoreError: false,
          actionType: 'ajax',
          outputVar: 'responseResult',
          options: {},
          api: {
            url: '${baseUrl}/act/manage/startAndComplete',
            method: 'post',
            data: {},
            requestAdaptor: '',
            adaptor: '',
            messages: {
              success: '提交审核成功！',
              failed: '提交审核失败！',
            },
          },
        },
      ],
    },
  });
  const env = reactive({
    process: {
      businessKey: '',
    },
    baseUrl: glob.domainUrl, // 配置全局的baseUrl
    userInfo: {
      id: unref(userStore.getUserInfo).id,
      realname: unref(userStore.getUserInfo).realname,
      username: unref(userStore.getUserInfo).username,
    },
  });
  const errorText = ref<string>('请联系管理员进行表单配置！');
  // 计算属性：检测schema是否为空
  const isSchemaEmpty = computed(() => {
    return Object.keys(schema).length === 0;
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ height: 300 });
    //加载amis页面结构数据，并设置modal的高度
    await loadSchema(data);
    setTimeout(() => {
      let scrollHeight = amisRenderer.value.$el.scrollHeight;
      scrollHeight = scrollHeight > 600 ? 600 : scrollHeight;
      setModalProps({ height: scrollHeight });
    }, 500);
  });

  /**
   * 清空schema对象中的所有属性
   */
  const clearSchema = () => {
    // 方法1：删除所有属性
    Object.keys(schema).forEach((key) => delete schema[key]);
  };
  /**
   * 加载页面结构数据
   *
   * @returns 无返回值
   */
  const loadSchema = async (data) => {
    try {
      clearSchema();
      const res = await defHttp.get({ url: '/dwf/form/queryById', params: { id: data.formId } }, { isTransformResponse: false });
      if (!res?.success || !res.result?.jsonSchema) return;
      const parsedSchema = JSON.parse(res.result.jsonSchema);
      switch (props.feat) {
        case 'Insert':
          //设置初次提交的参数
          onEvent.submitSucc.actions[0].api.url = '${baseUrl}/act/manage/startAndComplete';
          onEvent.submitSucc.actions[0].api.data = { businessKey: '${event.data.result.data.id}', processKey: data.processKey, formId: data.formId };
          //新建页面，需要将feat改为Insert并删除initApi属性
          Object.assign(schema, processFormAddObject(parsedSchema));
          break;
        case 'View':
          //设置全局变量
          env.process.businessKey = data.businessKey;
          //编辑页面，直接赋值即可
          Object.assign(schema, processFormViewObject(parsedSchema));
          break;
        default:
          //设置全局变量
          env.process.businessKey = data.businessKey;
          //设置编辑提交的参数
          onEvent.submitSucc.actions[0].api.url = '${baseUrl}/act/manage/complete';
          onEvent.submitSucc.actions[0].api.data = {
            taskId: data.taskId,
            opType: 'submit',
            opDesc: '提交',
            comment: '',
            variables: [],
            backTaskKey: null,
          };
          //编辑页面，直接赋值即可
          Object.assign(schema, processFormEditObject(parsedSchema));
      }
    } catch (e) {
      console.error('Failed to load schema:', e);
    }
  };
  /**
   * 新增的json处理函数，用于将表单的feat属性从'Edit'改为'Insert'并删除initApi
   * @param data 原始数据对象
   */
  function processFormAddObject(data) {
    // 递归搜索所有type为form的对象
    function findForms(obj, result: any = []) {
      if (typeof obj !== 'object' || obj === null) return result;

      if (Array.isArray(obj)) {
        for (const item of obj) {
          findForms(item, result);
        }
      } else {
        // 检查当前对象是否为form
        if (obj.type === 'form') {
          result.push(obj);
        }

        // 递归检查所有属性
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            findForms(obj[key], result);
          }
        }
      }
      return result;
    }

    // 查找所有form对象
    const forms = findForms(data);

    // 处理每个form对象
    forms.forEach((form) => {
      if (form.feat === 'Edit') {
        // 将feat改为Insert
        form.feat = 'Insert';
        // 删除initApi属性
        if ('initApi' in form) {
          delete form.initApi;
        }
        // 替换onEvent属性
        form.onEvent = onEvent;
      }
    });

    return data;
  }
  /**
   * 编辑的json处理函数，用于将表单的feat属性从'Edit'改为'Insert'并删除initApi
   * @param data 原始数据对象
   */
  function processFormEditObject(data) {
    // 递归搜索所有type为form的对象
    function findForms(obj, result: any = []) {
      if (typeof obj !== 'object' || obj === null) return result;

      if (Array.isArray(obj)) {
        for (const item of obj) {
          findForms(item, result);
        }
      } else {
        // 检查当前对象是否为form
        if (obj.type === 'form') {
          result.push(obj);
        }

        // 递归检查所有属性
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            findForms(obj[key], result);
          }
        }
      }
      return result;
    }

    // 查找所有form对象
    const forms = findForms(data);
    // 处理每个form对象
    forms.forEach((form) => {
      if (form.feat === 'Edit') {
        // 替换onEvent属性
        form.onEvent = onEvent;
      }
    });

    return data;
  }
  /**
   * 查看的json处理函数，用于将表单的feat属性从'Edit'改为'View'并设置为静态
   * @param data 原始数据对象
   */
  function processFormViewObject(data) {
    // 递归遍历对象
    function traverse(obj) {
      if (!obj || typeof obj !== 'object') return;

      // 检查当前对象是否是目标表单
      if (obj.type === 'form' && obj.feat === 'Edit') {
        // 修改表单属性
        obj.feat = 'View';
        // obj.static = true;

        // 删除api属性（如果存在）
        if ('api' in obj) {
          delete obj.api;
        }
      }

      // 递归处理对象属性
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          const value = obj[key];

          // 递归处理数组和对象
          if (Array.isArray(value)) {
            value.forEach((item) => traverse(item));
          } else if (typeof value === 'object' && value !== null) {
            traverse(value);
          }
        }
      }
    }

    traverse(data);
    return data;
  }
  const handleCancel = () => {
    closeModal();
    emit('close');
  };
</script>
<style scoped lang="less">
  .form-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
  }
</style>
