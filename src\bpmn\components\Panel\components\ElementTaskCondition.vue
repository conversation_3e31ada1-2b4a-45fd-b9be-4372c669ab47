<template>
  <n-collapse-item name="element-task-condition">
    <template #header>
      <collapse-title title="通过条件">
        <lucide-icon name="Check" />
      </collapse-title>
      <div style="margin-left: 3px; color: red">*</div>
    </template>
    <template #header-extra>
      <n-tag type="primary" round>
        {{ conditionNum }}
      </n-tag>
    </template>
    <edit-item label="条件设置" :label-width="120">
      <n-input v-model:value="condition" @change="updateCondition" />
    </edit-item>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent } from 'vue';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import { mapState } from 'pinia';
  import modelerStore from '/@/bpmn/store/modeler';
  import { setConditionValue } from '/@/bpmn/bo-utils/userTaskCondition';
  export default defineComponent({
    name: 'ElementTaskCondition',
    data() {
      return {
        condition: '' as any,
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
      conditionNum() {
        if (this.condition != null) {
          if (this.condition.length != 0) {
            return 1;
          } else {
            return 0;
          }
        } else {
          return 0;
        }
      },
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      reloadExtensionProperties() {
        let element = this.getActive;
        let body = null;
        const businessObject = element?.businessObject;
        if (businessObject.loopCharacteristics != undefined) {
          var loopCharacteristics = businessObject.loopCharacteristics;
          if (loopCharacteristics.hasOwnProperty('completionCondition')) {
            body = this.removeEqualSign(loopCharacteristics.completionCondition.body);
          }
        }
        this.condition = body;
      },
      removeEqualSign(str) {
        if (str && str[0] === '=') {
          return str.substring(1);
        } else {
          return str;
        }
      },
      updateCondition(value) {
        setConditionValue(this.getActive, value);
      },
    },
  });
</script>
