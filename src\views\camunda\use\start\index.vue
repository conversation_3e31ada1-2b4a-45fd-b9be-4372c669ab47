<template>
  <div>
    <a-button type="primary" @click="start">发起</a-button>
    <a-button type="primary" @click="complete">同意</a-button>
    <a-button type="primary" @click="bakc">退回</a-button>
  </div>
</template>

<script setup lang="ts">
  import { defHttp } from '/@/utils/http/axios';

  enum Api {
    start = '/act/manage/start',
    complete = '/act/manage/complete',
    back = '/act/manage/rollbackToStart',
  }
  function start() {
    const params = {
      processKey: 'buy',
      businessKey: '6cc02cf22b2fc2c5825b762f0dc39079',
    };
    //提交表单
    defHttp.post({ url: Api.start, params: params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        debugger;
      }
    });
  }
  function complete() {
    const params = {
      taskId: '8ea0eec3-4cd2-11f0-9988-047f0e0e49c4',
      comment: '同意',
    };
    //提交表单
    defHttp.post({ url: Api.complete, params: params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        debugger;
      }
    });
  }
  function bakc() {
    const params = {
      taskId: '8ea0eec3-4cd2-11f0-9988-047f0e0e49c4',
      comment: '同意',
    };
    //提交表单
    defHttp.post({ url: Api.back, params: params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        debugger;
      }
    });
  }
</script>

<style scoped></style>
