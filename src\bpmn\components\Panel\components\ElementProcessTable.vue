<template>
  <n-collapse-item name="element-table">
    <template #header>
      <collapse-title title="表列映射">
        <lucide-icon name="Database" />
      </collapse-title>
      <div style="margin-left: 3px; color: red">*</div>
    </template>
    <a-descriptions bordered size="small">
      <a-descriptions-item :span="3" label="动态表单">
        <a-switch :checked="oldProperty.isDynamic == 'Y'" :disabled="true" />
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="关联表" style="width: 50%">
        {{ oldProperty.tableName }}
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="主键列" style="width: 50%">
        {{ oldProperty.tableKey }}
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="状态列" style="width: 50%">
        {{ oldProperty.stateCode }}
      </a-descriptions-item>
    </a-descriptions>
    <a-button type="primary" preIcon="ant-design:setting-outlined" class="inline-large-button" @click="openPropertyModel()"> 配置表列映射 </a-button>
    <a-modal v-model:open="modelVisible" centered title="配置表列映射" :style="{ width: '640px' }">
      <a-card>
        <n-form ref="formRef" label-placement="left" :model="newProperty" :rules="rules" aria-modal="true" label-width="100px">
          <a-row>
            <a-col :span="24">
              <n-form-item label="动态表单" path="isDynamic">
                <n-radio-group v-model:value="newProperty.isDynamic" @update:value="changeDynamic">
                  <n-space>
                    <n-radio value="Y"> 是 </n-radio>
                    <n-radio value="N"> 否 </n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
            </a-col>
          </a-row>
          <a-row v-show="newProperty.isDynamic == 'N' ? true : false">
            <a-col :span="24">
              <n-form-item label="关联表" path="tableName">
                <n-input v-model:value="newProperty.tableName" @keydown.enter.prevent placeholder="请输入关联表" />
              </n-form-item>
            </a-col>
          </a-row>
          <a-row v-show="newProperty.isDynamic == 'N' ? true : false">
            <a-col :span="12">
              <n-form-item label="主键列" path="tableKey">
                <n-input v-model:value="newProperty.tableKey" @keydown.enter.prevent placeholder="请输入主键列" />
              </n-form-item>
            </a-col>
            <a-col :span="12">
              <n-form-item label="状态列" path="stateCode">
                <n-input v-model:value="newProperty.stateCode" @keydown.enter.prevent placeholder="请输入关联表" />
              </n-form-item>
            </a-col>
          </a-row>
        </n-form>
      </a-card>
      <template #footer>
        <a-button key="back" @click="modelVisible = false">取消</a-button>
        <a-button key="submit" type="primary" @click="addProperty">确认</a-button>
      </template>
    </a-modal>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent, markRaw } from 'vue';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import { mapState } from 'pinia';
  import modelerStore from '/@/bpmn/store/modeler';
  import { addExtensionProperty, getExtensionProperties, removeExtensionProperty } from '/@/bpmn/bo-utils/extensionPropertiesUtil';
  import { FormInst } from 'naive-ui';
  export default defineComponent({
    name: 'ElementProcessTable',
    data() {
      return {
        propertyName: 'proc_cus_table',
        extensionsRaw: [],
        extensions: [],
        oldProperty: { tableName: '', tableKey: '', stateCode: '', isDynamic: 'N' },
        newProperty: { tableName: '', tableKey: '', stateCode: '', isDynamic: 'N' },
        rules: {
          tableName: { level: 'warning', required: true, message: '关联表不能为空', trigger: ['blur', 'change'] },
          tableKey: { level: 'warning', required: true, message: '主键列不能为空', trigger: ['blur', 'change'] },
          stateCode: { level: 'warning', required: true, message: '状态列不能为空', trigger: ['blur', 'change'] },
        },
        modelVisible: false,
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      async reloadExtensionProperties() {
        let self = this;
        this.modelVisible = false;
        await this.$nextTick();
        (this.extensionsRaw as any[]) = markRaw(getExtensionProperties(this.getActive));
        this.extensions = JSON.parse(JSON.stringify(this.extensionsRaw));
        if (this.extensions.length > 0) {
          const extension = this.extensions.find((extension) => extension['name'] === self.propertyName);
          if (extension) {
            //json字符串转对象
            this.newProperty = JSON.parse(extension['value']);
            this.oldProperty = JSON.parse(extension['value']);
          }
        }
      },
      changeDynamic(value) {
        let required = true;
        if (value === 'Y') {
          this.newProperty.tableName = '';
          this.newProperty.tableKey = '';
          this.newProperty.stateCode = '';
          required = false;
        } else {
          required = true;
        }
        this.rules.tableName.required = required;
        this.rules.tableKey.required = required;
        this.rules.stateCode.required = required;
      },
      //打开窗口
      async openPropertyModel() {
        this.modelVisible = true;
        await this.$nextTick();
        (this.$refs.formRef as FormInst).restoreValidation();
        this.newProperty = Object.assign({}, this.oldProperty);
      },
      //添加属性
      async addProperty() {
        let self = this;
        let property = {
          name: self.propertyName,
          value: JSON.stringify(self.newProperty),
        };
        (this.$refs.formRef as FormInst).validate(async (_errors, extra) => {
          if (!extra.warnings) {
            self.removeProperty();
            addExtensionProperty(self.getActive, property);
            await self.reloadExtensionProperties();
          }
        });
      },
      //删除属性
      removeProperty() {
        let self = this;
        const propIndex = getExtensionProperties(this.getActive).findIndex((extension) => extension['name'] === self.propertyName);
        removeExtensionProperty(this.getActive, this.extensionsRaw[propIndex]);
      },
    },
  });
</script>

<style scoped></style>
