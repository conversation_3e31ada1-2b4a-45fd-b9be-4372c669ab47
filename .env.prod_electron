# 是否启用mock
VITE_USE_MOCK = false

# 后台接口父地址(必填)
# 【Electron下需要与 VITE_GLOB_DOMAIN_URL 配置保持一致】
VITE_GLOB_API_URL=http://127.0.0.1:9092/tld_ai

# 后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://127.0.0.1:9092/tld_ai

# 接口父路径前缀
VITE_GLOB_API_URL_PREFIX=

# 在线文档编辑版本。可选属性：wps, offlineWps(离线版), onlyoffice
VITE_GLOB_ONLINE_DOCUMENT_VERSION=wps

# 全局隐藏哪些布局。可选属性：sider,header,multi-tabs；多个用逗号隔开
#VITE_GLOB_HIDE_LAYOUT_TYPES=sider,header,multi-tabs

# -----------------------------------------
# ------------ 以下参数不建议修改 ------------
# -----------------------------------------

# 发布路径
# 【election下只能是 . 开头的相对路径，建议为 ./ 】
VITE_PUBLIC_PATH = ./

# 是否启用gzip或brotli压缩
# 选项值: gzip | brotli | none
# 如果需要多个可以使用“,”分隔
# 【electron下由于是本地html文件访问，所以不需要压缩】
VITE_BUILD_COMPRESS = 'none'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# ※ 请勿修改此项 ※
VITE_GLOB_RUN_PLATFORM=electron
