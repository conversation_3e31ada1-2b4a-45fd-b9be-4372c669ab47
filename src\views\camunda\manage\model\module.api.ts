import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/act/app/model/list',
  save = '/act/app/model/saveOrUpdate',
  edit = '/act/app/model/saveOrUpdate',
  get = '/act/app/model/queryById',
  delete = '/act/app/model/delete',
}
/**
 * 查询示例列表
 * @param params
 */
export const getList = (params) => {
  return defHttp.get({ url: Api.list, params });
};

/**
 * 保存或者更新示例
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 查询示例详情
 * @param params
 */
export const getDataById = (params) => {
  return defHttp.get({ url: Api.get, params });
};

/**
 * 删除示例
 * @param params
 */
export const deleteData = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
