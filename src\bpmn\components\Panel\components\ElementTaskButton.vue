<template>
  <n-collapse-item name="element-task-button">
    <template #header>
      <collapse-title title="操作权限">
        <lucide-icon name="Keyboard" />
      </collapse-title>
      <div style="margin-left: 3px; color: red">*</div>
    </template>
    <template #header-extra>
      <n-tag type="primary" round>
        {{ btnNums }}
      </n-tag>
    </template>
    <a-descriptions bordered size="small">
      <a-descriptions-item :span="3" label="流程提交">
        <a v-if="newProperty.isSubmit == 'Y'"> <a-icon type="check" style="margin-right: 3px; color: green" /><span style="color: green"></span></a>
        <a v-else> <a-icon type="stop" style="margin-right: 3px; color: red" /><span style="color: red"></span></a>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="审批通过">
        <a v-if="newProperty.isComplete == 'Y'"> <a-icon type="check" style="margin-right: 3px; color: green" /><span style="color: green"></span></a>
        <a v-else> <a-icon type="stop" style="margin-right: 3px; color: red" /><span style="color: red"></span></a>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="驳回至起点">
        <a v-if="newProperty.isReject == 'Y'"> <a-icon type="check" style="margin-right: 3px; color: green" /><span style="color: green"></span></a>
        <a v-else> <a-icon type="stop" style="margin-right: 3px; color: red" /><span style="color: red"></span></a>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="退回任意节点">
        <a v-if="newProperty.isBackAny == 'Y'"> <a-icon type="check" style="margin-right: 3px; color: green" /><span style="color: green"></span></a>
        <a v-else> <a-icon type="stop" style="margin-right: 3px; color: red" /><span style="color: red"></span></a>
      </a-descriptions-item>
    </a-descriptions>
    <a-button type="primary" preIcon="ant-design:unlock-outlined" class="inline-large-button" @click="openPropertyModel()">
      配置任务节点操作权限
    </a-button>
    <a-modal v-model:open="modelVisible" centered title="配置任务节点操作权限" :width="dialogWidth">
      <a-descriptions bordered>
        <a-descriptions-item label="流程提交" :span="3">
          <n-switch v-model:value="btnAuth.isSubmit" />
        </a-descriptions-item>
        <a-descriptions-item label="审批通过" :span="3">
          <n-switch v-model:value="btnAuth.isComplete" />
        </a-descriptions-item>
        <a-descriptions-item label="驳回至起点" :span="3">
          <n-switch v-model:value="btnAuth.isReject" />
        </a-descriptions-item>
        <a-descriptions-item label="退回任意节点" :span="3">
          <n-switch v-model:value="btnAuth.isBackAny" />
        </a-descriptions-item>
      </a-descriptions>
      <template #footer>
        <a-button key="back" @click="modelVisible = false">取消</a-button>
        <a-button key="submit" type="primary" @click="addProperty">确认</a-button>
      </template>
    </a-modal>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent, markRaw } from 'vue';
  import { addExtensionProperty, getExtensionProperties, removeExtensionProperty } from '/@/bpmn/bo-utils/extensionPropertiesUtil';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import { mapState } from 'pinia';
  import modelerStore from '/@/bpmn/store/modeler';
  export default defineComponent({
    name: 'ElementTaskButton',
    data() {
      return {
        extensionsRaw: [],
        propertyTaskName: 'proc_cus_task_btns',
        extensions: [],
        newProperty: { isSubmit: 'N', isComplete: 'N', isReject: 'N', isBackAny: 'N' },
        btnAuth: { isSubmit: false, isComplete: false, isReject: false, isBackAny: false },
        modelVisible: false,
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
      dialogWidth() {
        return '650px';
      },
      btnNums() {
        let num = 0;
        if (this.newProperty.isSubmit == 'Y') {
          num = num + 1;
        }
        if (this.newProperty.isComplete == 'Y') {
          num = num + 1;
        }
        if (this.newProperty.isReject == 'Y') {
          num = num + 1;
        }
        if (this.newProperty.isBackAny == 'Y') {
          num = num + 1;
        }
        return num;
      },
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      async reloadExtensionProperties() {
        let self = this;
        this.modelVisible = false;
        await this.$nextTick();
        //初始化
        this.newProperty = { isSubmit: 'N', isComplete: 'N', isReject: 'N', isBackAny: 'N' };
        //加载数据
        (this.extensionsRaw as any[]) = markRaw(getExtensionProperties(this.getActive));
        this.extensions = JSON.parse(JSON.stringify(this.extensionsRaw));
        if (this.extensions.length > 0) {
          const extension = this.extensions.find((extension) => extension['name'] === self.propertyTaskName);
          if (extension) {
            //json字符串转对象
            this.newProperty = JSON.parse(extension['value']);

            this.newProperty.isSubmit = this.newProperty.hasOwnProperty('isSubmit') ? this.newProperty.isSubmit : 'N';
            this.newProperty.isComplete = this.newProperty.hasOwnProperty('isComplete') ? this.newProperty.isComplete : 'N';
            this.newProperty.isComplete = this.newProperty.hasOwnProperty('isComplete') ? this.newProperty.isComplete : 'N';
            this.newProperty.isReject = this.newProperty.hasOwnProperty('isReject') ? this.newProperty.isReject : 'N';
            this.newProperty.isBackAny = this.newProperty.hasOwnProperty('isBackAny') ? this.newProperty.isBackAny : 'N';
          }
        }
      },

      async openPropertyModel() {
        this.reloadExtensionProperties();
        this.modelVisible = true;
        await this.$nextTick(() => {
          this.btnAuth.isSubmit = this.newProperty.isSubmit == 'Y' ? true : false;
          this.btnAuth.isComplete = this.newProperty.isComplete == 'Y' ? true : false;
          this.btnAuth.isReject = this.newProperty.isReject == 'Y' ? true : false;
          this.btnAuth.isBackAny = this.newProperty.isBackAny == 'Y' ? true : false;
        });
      },
      async addProperty() {
        let taskProperty = { name: this.propertyTaskName };
        taskProperty['value'] = JSON.stringify({
          isSubmit: this.btnAuth.isSubmit ? 'Y' : 'N',
          isComplete: this.btnAuth.isComplete ? 'Y' : 'N',
          isReject: this.btnAuth.isReject ? 'Y' : 'N',
          isBackAny: this.btnAuth.isBackAny ? 'Y' : 'N',
        });
        this.removeProperty();
        addExtensionProperty(this.getActive, taskProperty);
      },
      removeProperty() {
        let self = this;
        const propIndex = getExtensionProperties(this.getActive).findIndex((extension) => extension.name === self.propertyTaskName);
        removeExtensionProperty(this.getActive, this.extensionsRaw[propIndex]);
      },
    },
  });
</script>
