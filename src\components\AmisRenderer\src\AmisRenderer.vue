<template>
  <div id="amis-component"></div>
</template>

<script setup>
  import { onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { getToken } from '@/utils/auth';
  import axios from 'axios';

  function loadScript(src, callback) {
    const script = document.createElement('script');
    script.setAttribute('type', 'text/javascript');
    script.setAttribute('src', src);
    script.onload = () => callback();
    script.onerror = () => callback(new Error(`Failed to load ${src}`));
    document.body.appendChild(script);
  }

  function loadStyles(styles) {
    for (const path of styles) {
      const style = document.createElement('link');
      style.setAttribute('rel', 'stylesheet');
      style.setAttribute('type', 'text/css');
      style.setAttribute('href', path);
      document.head.appendChild(style);
    }
  }
  function loadSDK() {
    return new Promise((resolve, reject) => {
      if (window.amisRequire) {
        resolve();
        return;
      }
      loadStyles(['/amis/sdk/sdk.css', '/amis/sdk/helper.css', '/amis/sdk/iconfont.css', '/amis/sdk/cxd.css']);
      loadScript('/amis/sdk/sdk.js', (err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve();
      });
    });
  }

  const props = defineProps({
    /** The amis JSON schema */
    schema: {
      type: Object,
      required: true,
    },
    /** 赋予 amis 顶层数据域的值 */
    locals: {
      type: Object,
      default: () => ({}),
    },
    /** 控制 amis 的属性，一般都用不上 */
    props: {
      type: Object,
      default: () => ({}),
    },
    /** 环境变量，用于控制 amis 的行为，需要使用 amis 用户实现部分接口 */
    env: {
      type: Object,
      default: () => ({}),
    },
  });

  const updateProps = function () {
    this.amisInstance?.updateProps({
      data: {
        ...this.locals,
      },
      context: this.context,
      ...this.props,
    });
  };

  watch(
    () => props.locals,
    (newLocals, oldLocals) => {
      console.log(`count changed from ${oldLocals} to ${newLocals}`);
      updateProps(); // 直接调用函数，不再使用this
    },
    { deep: true }
  );

  watch(
    () => props.props,
    (newProps, oldProps) => {
      console.log(`count changed from ${newProps} to ${oldProps}`);
      updateProps(); // 直接调用函数，不再使用this
    },
    { deep: true }
  );

  watch(
    (newProps, oldProps) => {
      console.log(`count changed from ${newProps} to ${oldProps}`);
      updateProps;
    },
    { deep: true }
  );

  /** The amis component is ready */
  const emit = defineEmits(['ready']);

  let amisInstance = null;
  const router = useRouter();

  onMounted(async () => {
    await loadSDK();
    const scoped = window.amisRequire('amis/embed');
    const { normalizeLink } = amisRequire('amis-core');

    const instance = scoped.embed(
      '#amis-component',
      props.schema,
      {
        // 3.1.0 开始可以传入 context 数据，无论哪层都可以使用到这个里面的数据。适合用来传递一些平台数据。
        context: {
          ...props.env,
        },
        // 可以通过 props 里的 locals 属性来赋予 amis 顶层数据域的值
        data: {
          ...props.locals,
        },
        // 	其它的初始 props，一般不用传。
        ...props.props,
        // locale: 'en-US' // props 中可以设置语言，默认是中文
      },
      {
        // 下面三个接口必须实现
        fetcher: ({
          url, // 接口地址
          method, // 请求方法 get、post、put、delete
          data, // 请求数据
          responseType,
          config, // 其他配置
          headers, // 请求头
        }) => {
          config = config || {};
          config.withCredentials = true;
          responseType && (config.responseType = responseType);

          if (config.cancelExecutor) {
            config.cancelToken = new axios.CancelToken(config.cancelExecutor);
          }

          config.headers = headers || {};
          // 设置token
          const isToken = (config.headers || {}).isToken === false;
          if (getToken() && !isToken) {
            config.headers['X-Access-Token'] = getToken();
          }

          if (method !== 'post' && method !== 'put' && method !== 'patch') {
            if (data) {
              config.params = data;
            }

            return axios[method](url, config);
          } else if (data && data instanceof FormData) {
            config.headers = config.headers || {};
            config.headers['Content-Type'] = 'multipart/form-data';
          } else if (data && typeof data !== 'string' && !(data instanceof Blob) && !(data instanceof ArrayBuffer)) {
            data = JSON.stringify(data);
            config.headers = config.headers || {};
            config.headers['Content-Type'] = 'application/json';
          }
          return axios[method](url, data, config);
        },
      },
      () => {
        emit('ready', { instance });
      }
    );
    amisInstance = instance;
  });

  onUnmounted(() => {
    amisInstance?.unmount();
  });
  defineExpose({
    getAmisInstance: () => amisInstance,
  });
</script>
