<template>
  <div style="display: flex; justify-content: center">
    <a-descriptions bordered style="width: 600px; margin-top: 50px">
      <a-descriptions-item label="申请人" :span="3">
        <n-switch v-model:value="selRelation.submitUser" />
      </a-descriptions-item>
      <a-descriptions-item label="申请人部门" :span="3">
        <n-switch v-model:value="selRelation.submitOrg" />
      </a-descriptions-item>
      <a-descriptions-item label="申请人角色" :span="3">
        <n-switch v-model:value="selRelation.submitRole" />
      </a-descriptions-item>
      <a-descriptions-item label="申请人职位" :span="3">
        <n-switch v-model:value="selRelation.submitPosition" />
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  export default defineComponent({
    props: {
      relation: {
        type: Object,
      },
    },
    data() {
      return {
        selRelation: {
          submitUser: false,
          submitOrg: false,
          submitRole: false,
          submitPosition: false,
        },
      };
    },
    methods: {
      refresh() {
        this.setSelRelation();
      },
      setSelRelation() {
        this.selRelation.submitUser = this.relation.submitUser == 'Y' ? true : false;
        this.selRelation.submitOrg = this.relation.submitOrg == 'Y' ? true : false;
        this.selRelation.submitRole = this.relation.submitRole == 'Y' ? true : false;
        this.selRelation.submitPosition = this.relation.submitPosition == 'Y' ? true : false;
      },
    },
    mounted() {
      this.refresh();
    },
    created() {},
    computed: {},
    watch: {
      relation: function (newValue, oldValue) {
        this.setSelRelation();
      },
    },
  });
</script>

<style></style>
