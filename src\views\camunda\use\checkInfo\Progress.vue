<template>
  <div style="padding: 16px 0px 0px 0px; height: 616px; background-color: white">
    <div class="bpmn-header">
      <div style="float: left">
        <div style="display: flex; align-items: center">
          <div style="background-color: #9de1b1; width: 60px; height: 20px; margin-left: 20px"></div>
          <div class="bpmn-header-font">已完成</div>
        </div>
      </div>
      <div style="float: left; margin-left: 30px">
        <div style="display: flex; align-items: center">
          <div style="background-color: #fffa9e; width: 60px; height: 20px"></div>
          <div class="bpmn-header-font">待处理</div>
        </div>
      </div>
      <div style="float: right; margin-right: 20px">
        <a-button-group>
          <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleZoom(0.2)" />
          <a-button type="primary" preIcon="ant-design:minus-outlined" @click="handleZoom(-0.2)" />
          <a-button type="primary" preIcon="ant-design:reload-outlined" @click="restoreZoom()" />
        </a-button-group>
      </div>
    </div>
    <!-- <div class="bpmn-container"> -->
    <div class="canvas" ref="canvasRef"></div>
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, nextTick } from 'vue';
  import BpmnViewer from 'bpmn-js/lib/Viewer';
  import MoveCanvasModule from 'diagram-js/lib/navigation/movecanvas';
  import { defHttp } from '/@/utils/http/axios';

  enum Api {
    bpmnXml = '/act/manage/getBpmn',
  }

  const bpmnViewer = ref<any>(null);
  const canvasRef = ref<any>(null);
  const bpmnStr = ref('');
  const completeNodes = reactive<any[]>([]);
  const activeNodes = reactive<any[]>([]);
  const scale = ref(1);
  const props = defineProps({
    procInstId: {
      type: String,
      default: '',
    },
  });
  function init() {
    // 建模
    bpmnViewer.value = new BpmnViewer({
      container: canvasRef.value,
      additionalModules: [
        MoveCanvasModule, // 移动整个画布
      ],
    });
    // 隐藏左侧工具栏
    createNewDiagram();
  }
  function createNewDiagram() {
    bpmnViewer.value.importXML(bpmnStr.value, (err) => {
      if (!err) {
        let canvas = bpmnViewer.value.get('canvas');
        canvas.zoom('fit-viewport', 'auto');
        setNodeColor(activeNodes, 'processing', canvas);
        setNodeColor(completeNodes, 'complete', canvas);
        // 获取任务节点元素对象
        const elementRegistry = bpmnViewer.value.get('elementRegistry');
        elementRegistry.forEach(function (element) {
          if (isSequenceFlow(element)) {
            // 判断是否为连线
            const sourceNode = element.source;
            const targetNode = element.target;
            if (completeNodes.includes(sourceNode.id) && completeNodes.includes(targetNode.id)) {
              canvas.addMarker(element.id, 'complete-line');
            }
            if (completeNodes.includes(sourceNode.id) && activeNodes.includes(targetNode.id)) {
              canvas.addMarker(element.id, 'complete-line');
            }
          }
        });
      }
    });
  }
  // 设置高亮颜色的class
  function setNodeColor(nodeCodes, colorClass, canvas) {
    for (let i = 0; i < nodeCodes.length; i++) {
      canvas.addMarker(nodeCodes[i], colorClass);
    }
  }
  //判断是否是连线
  function isSequenceFlow(element) {
    return element.type === 'bpmn:SequenceFlow';
  }
  // 放大缩小，这里尽量设置flag的值小一点，这样每次放大缩小不会很多，避免放大超出父元素
  async function handleZoom(flag) {
    scale.value += flag;
    await nextTick();
    bpmnViewer.value.get('canvas').zoom(scale.value);
  }
  //恢复原始大小
  async function restoreZoom() {
    scale.value = 1;
    await nextTick();
    let canvas = bpmnViewer.value.get('canvas');
    canvas.zoom(scale.value);
    canvas.zoom('fit-viewport', 'auto');
  }
  function getBpmnXml() {
    let params = {
      procInstId: props.procInstId,
    };
    defHttp.get({ url: Api.bpmnXml, params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        bpmnStr.value = res.result.bpmnXml;
        res.result.taskKeyArr.forEach((item) => {
          if (item.state == 'active') {
            activeNodes.push(item.taskKey);
          } else {
            completeNodes.push(item.taskKey);
          }
        });
        init();
      }
    });
  }

  onMounted(() => {
    getBpmnXml();
  });
</script>

<style>
  a.bjs-powered-by {
    display: none;
  }
  .complete-line g.djs-visual > :nth-child(1) {
    stroke: #98e0ad !important;
  }
  .complete:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: #98e0ad !important;
    stroke: #98e0ad !important;
  }
  .processing:not(.djs-connection) .djs-visual > :nth-child(1) {
    fill: #fffa99 !important;
    stroke: #fffa99 !important;
  }
</style>
<style scoped>
  .bpmn-header {
    height: 40px;
    border-bottom: 1px solid #cccccc;
  }
  .bpmn-header-font {
    margin-left: 5px;
    font-family: 'Microsoft Yahei', sans-serif;
    font-weight: bold;
  }
  /* .bpmn-container {
    position: absolute;
    background-color: #ffffff;
    width: 100%;
    height: calc(100% - 30px);
  } */
  .canvas {
    width: 100%;
    height: calc(100% - 30px);
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+')
      repeat !important;
    background-repeat: no-repeat;
    background-size: cover;
  }
</style>
