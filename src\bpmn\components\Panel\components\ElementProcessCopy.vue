<template>
  <n-collapse-item name="element-copy">
    <template #header>
      <collapse-title title="人员抄送">
        <lucide-icon name="Mail" />
      </collapse-title>
    </template>
    <a-descriptions bordered size="small">
      <a-descriptions-item :span="3" label="开启状态">
        <a-switch :checked="isOpen" :disabled="true" />
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="候选规则" v-if="newProperty.isCopyAllow == 'Y'">
        {{ newProperty.userRule == 'union' ? '并集' : '交集' }}
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="已选用户数" v-if="newProperty.isCopyAllow == 'Y'">
        <div :style="{ color: userList.length == 0 ? 'red' : 'green' }">{{ userList.length }}</div>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="已选部门数" v-if="newProperty.isCopyAllow == 'Y'">
        <div :style="{ color: departList.length == 0 ? 'red' : 'green' }">{{ departList.length }}</div>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="已选角色数" v-if="newProperty.isCopyAllow == 'Y'">
        <div :style="{ color: roleList.length == 0 ? 'red' : 'green' }">{{ roleList.length }}</div>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="已选职位数" v-if="newProperty.isCopyAllow == 'Y'">
        <div :style="{ color: positionList.length == 0 ? 'red' : 'green' }">{{ positionList.length }}</div>
      </a-descriptions-item>
      <a-descriptions-item :span="3" label="已选关系数" v-if="newProperty.isCopyAllow == 'Y'">
        <div :style="{ color: getRelationNum == 0 ? 'red' : 'green' }">{{ getRelationNum }}</div>
      </a-descriptions-item>
    </a-descriptions>
    <a-button type="primary" preIcon="ant-design:usergroup-add-outlined" class="inline-large-button" @click="openPropertyModel()">
      配置人员抄送
    </a-button>
    <a-modal v-model:open="modelVisible" centered title="配置人员抄送" :width="dialogWidth">
      <div style="padding: 20px 20px 0px 20px">
        <n-form ref="formRef" label-placement="left" :model="newProperty" aria-modal="true" label-width="100px">
          <a-row>
            <a-col :span="6">
              <n-form-item label="开启状态：">
                <n-switch v-model:value="switchVal" checked-value="Y" unchecked-value="N"> </n-switch>
              </n-form-item>
            </a-col>
            <a-col :span="6" v-show="switchVal == 'Y'">
              <n-form-item label="候选规则：">
                <n-radio-group v-model:value="userRule">
                  <n-space>
                    <n-radio value="union"> 并集 </n-radio>
                    <n-radio value="intersection"> 交集 </n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
            </a-col>
          </a-row>
        </n-form>
      </div>
      <div style="padding: 0px 20px 20px 20px">
        <a-tabs v-model:activeKey="activeTab" v-show="switchVal == 'Y'" size="small" style="height: 680px">
          <a-tab-pane key="user">
            <template #tab>
              <span>
                <UserOutlined></UserOutlined>
                用户
              </span>
            </template>
            <user-select ref="userSelect" :userList="userList"></user-select>
          </a-tab-pane>
          <a-tab-pane key="org">
            <template #tab>
              <span>
                <ClusterOutlined></ClusterOutlined>
                部门
              </span>
            </template>
            <org-select ref="orgSelect" :departList="departList"></org-select>
          </a-tab-pane>
          <a-tab-pane key="role">
            <template #tab>
              <span>
                <SolutionOutlined></SolutionOutlined>
                角色
              </span>
            </template>
            <role-select ref="roleSelect" :roleList="roleList"></role-select>
          </a-tab-pane>
          <a-tab-pane key="position">
            <template #tab>
              <span>
                <ContactsOutlined></ContactsOutlined>
                职位
              </span>
            </template>
            <position-select ref="positionSelect" :positionList="positionList"></position-select>
          </a-tab-pane>
          <a-tab-pane key="relation">
            <template #tab>
              <span>
                <BranchesOutlined></BranchesOutlined>
                关系
              </span>
            </template>
            <relation-select ref="relationSelect" :relation="relation"></relation-select>
          </a-tab-pane>
        </a-tabs>
      </div>
      <template #footer>
        <a-button key="back" @click="modelVisible = false">取消</a-button>
        <a-button key="submit" type="primary" @click="addProperty">确认</a-button>
      </template>
    </a-modal>
  </n-collapse-item>
</template>

<script lang="ts">
  import { defineComponent, markRaw } from 'vue';
  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import modelerStore from '/@/bpmn/store/modeler';
  import { mapState } from 'pinia';
  import { addExtensionProperty, getExtensionProperties, removeExtensionProperty } from '/@/bpmn/bo-utils/extensionPropertiesUtil';
  import UserSelect from './modules/UserSelect.vue';
  import OrgSelect from './modules/OrgSelect.vue';
  import RoleSelect from './modules/RoleSelect.vue';
  import PositionSelect from './modules/PositionSelect.vue';
  import RelationSelect from './modules/RelationSelect.vue';
  import { UserOutlined, ClusterOutlined, ContactsOutlined, SolutionOutlined, BranchesOutlined } from '@ant-design/icons-vue';
  export default defineComponent({
    name: 'ElementProcessCopy',
    components: {
      UserSelect,
      OrgSelect,
      RoleSelect,
      PositionSelect,
      RelationSelect,
      UserOutlined,
      ClusterOutlined,
      ContactsOutlined,
      SolutionOutlined,
      BranchesOutlined,
    },
    data() {
      return {
        extensionsRaw: [],
        activeTab: 'user',
        isOpen: false,
        switchVal: 'N',
        userRule: 'union', //默认并集
        propertyCopyName: 'proc_cus_copy',
        extensions: [],
        newProperty: { isCopyAllow: 'N', userRule: 'union' },
        modelVisible: false,
        userList: [],
        roleList: [],
        positionList: [],
        departList: [],
        relation: {
          submitUser: 'N',
          submitOrg: 'N',
          submitRole: 'N',
          submitPosition: 'N',
        },
      };
    },
    computed: {
      ...mapState(modelerStore, ['getActive', 'getActiveId']),
      dialogWidth() {
        return this.switchVal == 'Y' ? '1300px' : '640px';
      },
      getRelationNum() {
        let count = 0;
        if (this.relation.submitUser == 'Y') {
          count = count + 1;
        }
        if (this.relation.submitOrg == 'Y') {
          count = count + 1;
        }
        if (this.relation.submitRole == 'Y') {
          count = count + 1;
        }
        if (this.relation.submitPosition == 'Y') {
          count = count + 1;
        }
        return count;
      },
    },
    mounted() {
      this.reloadExtensionProperties();
      EventEmitter.on('element-update', this.reloadExtensionProperties);
    },
    methods: {
      async reloadExtensionProperties() {
        let self = this;
        this.modelVisible = false;
        await this.$nextTick();
        (this.extensionsRaw as any[]) = markRaw(getExtensionProperties(this.getActive));
        this.extensions = JSON.parse(JSON.stringify(this.extensionsRaw));
        if (this.extensions.length > 0) {
          const extension = this.extensions.find((extension) => extension['name'] === self.propertyCopyName);
          if (extension) {
            //json字符串转对象
            this.newProperty = JSON.parse(extension['value']);
            if (this.newProperty.hasOwnProperty('authUsers')) {
              const authUsers = this.newProperty['authUsers'];
              this.userList = authUsers.hasOwnProperty('userList') ? authUsers.userList : [];
              this.roleList = authUsers.hasOwnProperty('roleList') ? authUsers.roleList : [];
              this.positionList = authUsers.hasOwnProperty('positionList') ? authUsers.positionList : [];
              this.departList = authUsers.hasOwnProperty('departList') ? authUsers.departList : [];
              this.relation.submitUser = authUsers.hasOwnProperty('submitUser') ? authUsers.submitUser : 'N';
              this.relation.submitOrg = authUsers.hasOwnProperty('submitOrg') ? authUsers.submitOrg : 'N';
              this.relation.submitRole = authUsers.hasOwnProperty('submitRole') ? authUsers.submitRole : 'N';
              this.relation.submitPosition = authUsers.hasOwnProperty('submitPosition') ? authUsers.submitPosition : 'N';
            } else {
              this.userList = [];
              this.roleList = [];
              this.positionList = [];
              this.departList = [];
              this.relation.submitUser = 'N';
              this.relation.submitOrg = 'N';
              this.relation.submitRole = 'N';
              this.relation.submitPosition = 'N';
            }
            if (this.newProperty.hasOwnProperty('userRule')) {
              this.userRule = this.newProperty.userRule;
            }
            if (this.newProperty.hasOwnProperty('isCopyAllow')) {
              this.isOpen = this.newProperty.isCopyAllow == 'Y' ? true : false;
              this.switchVal = this.newProperty.isCopyAllow;
            }
          }
        }
      },

      async openPropertyModel() {
        this.reloadExtensionProperties();
        this.modelVisible = true;
        await this.$nextTick(() => {
          this.activeTab = 'user';
        });
      },
      async addProperty() {
        let copyProperty = { name: this.propertyCopyName };
        if (this.switchVal == 'N') {
          //如果不开启抄送
          copyProperty['value'] = JSON.stringify({
            isCopyAllow: 'N',
          });
        } else {
          copyProperty['value'] = JSON.stringify({
            isCopyAllow: 'Y',
            userRule: this.userRule,
            authUsers: {
              userList: this.$refs.userSelect.selUserIds,
              departList: typeof this.$refs.orgSelect === 'undefined' ? this.departList : this.$refs.orgSelect['checkedKeys'],
              roleList: typeof this.$refs.roleSelect === 'undefined' ? this.roleList : this.$refs.roleSelect['selRoleIds'],
              positionList: typeof this.$refs.positionSelect === 'undefined' ? this.positionList : this.$refs.positionSelect['selPositionIds'],
              submitUser:
                typeof this.$refs.relationSelect === 'undefined'
                  ? this.relation.submitUser
                  : this.$refs.relationSelect.selRelation.submitUser
                    ? 'Y'
                    : 'N',
              submitOrg:
                typeof this.$refs.relationSelect === 'undefined'
                  ? this.relation.submitOrg
                  : this.$refs.relationSelect.selRelation.submitOrg
                    ? 'Y'
                    : 'N',
              submitRole:
                typeof this.$refs.relationSelect === 'undefined'
                  ? this.relation.submitRole
                  : this.$refs.relationSelect.selRelation.submitRole
                    ? 'Y'
                    : 'N',
              submitPosition:
                typeof this.$refs.relationSelect === 'undefined'
                  ? this.relation.submitPosition
                  : this.$refs.relationSelect.selRelation.submitPosition
                    ? 'Y'
                    : 'N',
            },
          });
        }
        this.removeProperty();
        addExtensionProperty(this.getActive, copyProperty);
      },
      removeProperty() {
        let self = this;
        const propIndex = getExtensionProperties(this.getActive).findIndex((extension) => extension.name === self.propertyCopyName);
        removeExtensionProperty(this.getActive, this.extensionsRaw[propIndex]);
      },
    },
  });
</script>

<style scoped></style>
