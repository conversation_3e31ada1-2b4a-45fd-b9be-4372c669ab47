<template>
  <div style="background-color: white">
    <a-timeline style="padding: 20px">
      <a-timeline-item v-for="log in dataSource" :key="log.id">
        <template #dot><a-icon :type="log.icon" :style="{ fontSize: '16px', color: log.color }" /></template>
        <div :style="{ color: log.color }">
          <span>{{ log.opDate }}&nbsp;&nbsp;{{ getTranType(log.opType) }}</span>
        </div>
        <a-descriptions bordered size="middle" layout="vertical" :column="3" style="margin-top: 5px">
          <a-descriptions-item label="任务节点"> {{ log.taskName }}</a-descriptions-item>
          <a-descriptions-item label="办理人员"> {{ log.opUser }}</a-descriptions-item>
          <a-descriptions-item label="接收日期"> {{ log.startDate }}</a-descriptions-item>
          <a-descriptions-item label="办理操作" v-if="log.opType != 'submit'"> {{ log.opDesc }}</a-descriptions-item>
          <a-descriptions-item label="办理意见" v-if="log.opType != 'submit'"> {{ log.opIdea }}</a-descriptions-item>
        </a-descriptions>
      </a-timeline-item>
    </a-timeline>
  </div>
</template>

<script setup lang="ts">
  import { reactive, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  //提示框组件对象
  const { createMessage } = useMessage();
  enum Api {
    log = '/act/log/queryAll',
  }

  const props = defineProps({
    procInstId: {
      type: String,
      default: '',
    },
  });

  const dataSource = reactive<any[]>([]);

  function loadLogs() {
    let params = {
      procInstId: props.procInstId,
    };
    defHttp.get({ url: Api.log, params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        Object.assign(dataSource, res.result);
        dataSource.forEach(function (obj) {
          switch (obj.opType) {
            case 'submit':
              obj.icon = 'form';
              obj.color = '#40a9ff';
              break;
            case 'reject':
              obj.icon = 'rollback';
              obj.color = '#f47378';
              break;
            case 'back':
              obj.icon = 'rollback';
              obj.color = '#f47378';
              break;
            case 'recall':
              obj.icon = 'rollback';
              obj.color = '#f47378';
              break;
            case 'transfer':
              obj.icon = 'form';
              break;
            default:
              obj.icon = 'check-circle';
              obj.color = '#6bd089';
          }
        });
      } else {
        createMessage.warning(res.message);
      }
    });
  }

  function getTranType(type) {
    let str = '';
    switch (type) {
      case 'submit':
        str = '提交';
        break;
      case 'reject':
        str = '驳回';
        break;
      case 'back':
        str = '退回';
        break;
      case 'recall':
        str = '撤回';
        break;
      case 'transfer':
        str = '转办';
        break;
      case 'jump':
        str = '跳转';
        break;
      default:
        str = '通过';
    }
    return str;
  }
  onMounted(() => {
    loadLogs();
  });
</script>

<style>
  /* .log-desc-item {
    display: flex;
    align-items: center;
  }
  .log-desc-item .ant-descriptions-item-label {
    text-align: right;
  }
  .log-desc-item .ant-descriptions-item-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  } */
  .log-text {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
  }
  .log {
    float: left;
    height: 50px;
  }
</style>
