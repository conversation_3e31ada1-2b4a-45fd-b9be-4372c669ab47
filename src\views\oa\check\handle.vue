<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #action="{ record }">
        <a @click="showForm(record)"> <FileSearchOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">工单明细</span></a>
        <a-divider type="vertical" />
        <a @click="openCheckInfoModal(record)">
          <FileSearchOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">审批详情</span></a
        >
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'state'">
          <span>
            <a-tag v-if="text == 'ACTIVE' && record.firstTask == 'N'" color="pink">进行中</a-tag>
            <a-tag v-if="text == 'COMPLETED' && record.firstTask == 'N'" color="green">已完成</a-tag>
            <a-tag v-if="record.firstTask == 'Y'" color="#FF4D4F">已退回</a-tag>
          </span>
        </template>
      </template>
    </BasicTable>
    <a-modal
      v-if="open"
      v-model:open="open"
      title="流程窗口"
      @cancel="closeModal"
      :footer="null"
      :keyboard="false"
      width="100%"
      wrap-class-name="flow-modal"
    >
      <CheckInfo
        :allowOperate="checkInfo.allowOperate"
        :taskId="checkInfo.taskId"
        :startUserName="checkInfo.startUserName"
        :procDefKey="checkInfo.procDefKey"
        :procDefName="checkInfo.procDefName"
        :procInstId="checkInfo.procInstId"
        :businessKey="checkInfo.businessKey"
        @close="closeModal"
      ></CheckInfo>
    </a-modal>
    <FormModal @register="registerModal" :title="title" :feat="feat" />
  </div>
</template>

<script setup lang="ts">
  import { ref, unref, watch, reactive } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Table';
  import { FileSearchOutlined, FormOutlined } from '@ant-design/icons-vue';
  import { defHttp } from '/@/utils/http/axios';
  import CheckInfo from '@/views/camunda/use/checkInfo/index.vue';
  import { useModal } from '/@/components/Modal';
  import FormModal from '/@/views/oa/components/FormModal.vue';

  const [registerModal, { openModal }] = useModal();
  //窗口属性
  const title = ref<string>('');
  const feat = ref<string>('View');

  enum Api {
    list = '/dwf/form/queryHandlerOrder',
  }
  //窗口开关
  const open = ref(false);
  //窗口参数对象
  const checkInfo = reactive({
    taskId: '',
    startUserName: '',
    procDefKey: '',
    procDefName: '',
    procInstId: '',
    businessKey: '',
    allowOperate: false,
  });
  const getList = (params) => {
    return defHttp.get({ url: Api.list, params });
  };
  const columns: BasicColumn[] = [
    {
      title: '工单名称',
      dataIndex: 'formName',
    },
    {
      title: '流程名称',
      dataIndex: 'procDefName',
    },
    {
      title: '发起时间',
      align: 'center',
      dataIndex: 'startTime',
      width: 200,
    },
    {
      title: '结束时间',
      align: 'center',
      dataIndex: 'endTime',
      width: 200,
    },
    {
      title: '流程耗时',
      align: 'center',
      dataIndex: 'duration',
      width: 200,
      customRender(obj) {
        const ms = obj.value;
        if (ms != null) {
          const day = Math.floor(ms / (24 * 60 * 60 * 1000));
          const hour = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
          const minute = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
          const second = Math.floor((ms % (60 * 1000)) / 1000);
          let val = '';
          if (day != 0) {
            val = val + day + '天';
          }
          if (hour != 0) {
            val = val + hour + '时';
          }
          if (minute != 0) {
            val = val + minute + '分';
          }
          if (second != 0) {
            val = val + second + '秒';
          }
          return val;
        } else {
          return ms;
        }
      },
    },
    {
      title: '完成状态',
      align: 'center',
      dataIndex: 'state',
      width: 150,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'formName',
      label: '工单名称',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      field: 'startUserName',
      label: '提交人',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];
  const [registerTable, { reload, setProps }] = useTable({
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: false,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  /**
   * 打开表单窗口
   * @param form
   */
  const showForm = (form) => {
    title.value = form.formName;
    openModal(true, { processKey: form.procDefKey, formId: form.formId, businessKey: form.businessKey });
  };
  /**
   * 打开详情窗口
   */
  function openCheckInfoModal(record) {
    checkInfo.businessKey = record.businessKey;
    checkInfo.procInstId = record.procInstId;
    checkInfo.procDefKey = record.procDefKey;
    checkInfo.procDefName = record.procDefName;
    checkInfo.startUserName = record.startUserName;
    checkInfo.taskId = record.id;
    open.value = true;
  }
  /**
   * 流程窗口关闭
   * @param record
   */
  function closeModal() {
    open.value = false;
    reload();
  }
  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less">
  .flow-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
      padding: 20px;
    }
  }
</style>
