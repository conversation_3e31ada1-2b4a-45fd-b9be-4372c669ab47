<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #action="{ record }">
        <a @click="showForm(record)"> <FileSearchOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">工单明细</span></a>
        <a-divider type="vertical" />
        <a @click="openCheckInfoModal(record)">
          <CheckSquareOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">工单审批</span></a
        >
      </template>
    </BasicTable>
    <a-modal
      v-if="open"
      v-model:open="open"
      title="流程窗口"
      @cancel="closeModal"
      :footer="null"
      :keyboard="false"
      width="100%"
      wrap-class-name="flow-modal"
    >
      <CheckInfo
        :allowOperate="checkInfo.allowOperate"
        :taskId="checkInfo.taskId"
        :startUserName="checkInfo.startUserName"
        :procDefKey="checkInfo.procDefKey"
        :procDefName="checkInfo.procDefName"
        :procInstId="checkInfo.procInstId"
        :businessKey="checkInfo.businessKey"
        @close="closeModal"
      ></CheckInfo>
    </a-modal>
    <FormModal @register="registerModal" :title="title" :feat="feat" />
  </div>
</template>

<script setup lang="ts">
  import { ref, unref, watch, reactive } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Table';
  import { FileSearchOutlined, CheckSquareOutlined } from '@ant-design/icons-vue';
  import { defHttp } from '/@/utils/http/axios';
  import CheckInfo from '@/views/camunda/use/checkInfo/index.vue';
  import { useModal } from '/@/components/Modal';
  import FormModal from '/@/views/oa/components/FormModal.vue';

  const [registerModal, { openModal }] = useModal();
  //窗口属性
  const title = ref<string>('');
  const feat = ref<string>('View');

  enum Api {
    list = '/dwf/form/queryTaskOrder',
  }
  //窗口开关
  const open = ref(false);
  //窗口参数对象
  const checkInfo = reactive({
    taskId: '',
    startUserName: '',
    procDefKey: '',
    procDefName: '',
    procInstId: '',
    businessKey: '',
    allowOperate: true,
  });
  const getList = (params) => {
    return defHttp.get({ url: Api.list, params });
  };
  const columns: BasicColumn[] = [
    {
      title: '工单名称 ',
      align: 'center',
      dataIndex: 'formName',
    },
    {
      title: '流程名称 ',
      align: 'center',
      dataIndex: 'procDefName',
    },
    {
      title: '提交人',
      align: 'center',
      dataIndex: 'startUserName',
    },
    {
      title: '提交时间',
      align: 'center',
      dataIndex: 'procStartTime',
    },
    {
      title: '当前节点',
      align: 'center',
      dataIndex: 'taskName',
    },
    {
      title: '委派人',
      align: 'center',
      dataIndex: 'owner',
      width: 150,
      customRender: function (obj) {
        const owner = obj.value;
        if (owner != null) {
          return owner;
        } else {
          return '自动流转';
        }
      },
    },
    {
      title: '委派时间',
      align: 'center',
      dataIndex: 'taskStartTime',
      width: 200,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'formName',
      label: '工单名称',
      component: 'Input',
      colProps: { span: 8 },
    },
    {
      field: 'startUserName',
      label: '提交人',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];
  const [registerTable, { reload, setProps }] = useTable({
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: false,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });
  /**
   * 打开表单窗口
   * @param form
   */
  const showForm = (form) => {
    title.value = form.formName;
    openModal(true, { processKey: form.procDefKey, formId: form.formId, businessKey: form.businessKey });
  };
  /**
   * 打开详情窗口
   */
  function openCheckInfoModal(record) {
    checkInfo.businessKey = record.businessKey;
    checkInfo.procInstId = record.procInstId;
    checkInfo.procDefKey = record.procDefKey;
    checkInfo.procDefName = record.procDefName;
    checkInfo.startUserName = record.startUserName;
    checkInfo.taskId = record.id;
    open.value = true;
  }
  /**
   * 流程窗口关闭
   * @param record
   */
  function closeModal() {
    open.value = false;
    reload();
  }
  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less">
  .flow-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
      padding: 20px;
    }
  }
</style>
