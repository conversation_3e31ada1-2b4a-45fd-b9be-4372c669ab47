import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/act/app/type/list',
  listAll = '/act/app/type/listAll',
  save = '/act/app/type/saveOrUpdate',
  edit = '/act/app/type/saveOrUpdate',
  get = '/act/app/type/queryById',
  delete = '/act/app/type/delete',
}
/**
 * 查询示例列表
 * @param params
 */
export const getList = (params) => {
  return defHttp.get({ url: Api.list, params });
};
/**
 * 查询示例列表
 * @param params
 */
export const getAllList = (params) => {
  return defHttp.get({ url: Api.listAll, params });
};
/**
 * 保存或者更新示例
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 查询示例详情
 * @param params
 */
export const getDataById = (params) => {
  return defHttp.get({ url: Api.get, params });
};

/**
 * 删除示例
 * @param params
 */
export const deleteData = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
