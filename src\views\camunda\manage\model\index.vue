<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #tableTitle>
        <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">新增</a-button>
      </template>
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.key === 'deployState'">
          <span>
            <a-tag v-if="text == 'INIT'" color="pink">设计</a-tag>
            <a-tag v-if="text == 'DEPLOY'" color="green">部署</a-tag>
          </span>
        </template>
        <template v-if="column.key === 'useState'">
          <span>
            <a-tag v-if="text == 'ACTIVE'" color="green">激活</a-tag>
            <a-tag v-if="text == 'SUSPEND'" color="pink">冻结</a-tag>
          </span>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a @click="openDesign(record)"><a-icon type="share-alt" style="margin-right: 3px" />设计</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)"><a-icon type="edit" style="margin-right: 3px" />编辑</a>

          <a-divider type="vertical" />
          <a-popconfirm title="是否删除？" ok-text="是" cancel-text="否" @confirm="handleDelete(record)">
            <a><DeleteOutlined style="margin-right: 3px; color: #eb2f96" /><span style="color: #eb2f96">删除</span></a>
          </a-popconfirm>
        </template>
      </template>
    </BasicTable>
    <ModuleModal @register="registerModal" @success="reload" :isDisabled="isDisabled" :types="typeArray" />
    <a-modal v-if="open" v-model:open="open" @cancel="closeDesign" :footer="null" :keyboard="false" width="100%" wrap-class-name="design-modal">
      <DesignPanel :xmlData="xmlData" :modelId="modelId"></DesignPanel>
    </a-modal>
  </div>
</template>

<script setup lang="ts" name="camunda-design">
  import { ref, reactive, unref, watch, onMounted } from 'vue';
  import DesignPanel from './components/DesignPanel.vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import ModuleModal from './components/ModuleModal.vue';
  import { getList, deleteData } from './module.api';
  import { searchFormSchema } from './module.data';
  import { BasicColumn } from '/@/components/Table';
  import { DeleteOutlined } from '@ant-design/icons-vue';
  import { defHttp } from '/@/utils/http/axios';

  const [registerModal, { openModal }] = useModal();
  const isDisabled = ref(false);
  const open = ref(false);
  const xmlData = ref('');
  const modelId = ref('');
  const typeArray = ref<any>([]);
  const columns: BasicColumn[] = reactive([
    {
      title: '模型编码',
      dataIndex: 'modelKey',
    },
    {
      title: '模型名称',
      dataIndex: 'modelName',
    },
    {
      title: '版本号',
      dataIndex: 'version',
      width: 100,
    },
    {
      title: '业务类型',
      dataIndex: 'typeId',
      width: 150,
      customRender: function ({ text }) {
        if (text == undefined) {
          return '';
        } else {
          return filterModelType(text);
        }
      },
    },
    {
      title: '部署状态',
      dataIndex: 'deployState',
      width: 100,
    },
    {
      title: '使用状态',
      dataIndex: 'useState',
      width: 100,
    },
    {
      title: '流程描述',
      dataIndex: 'remark',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 280,
    },
  ]);

  const [registerTable, { reload, setProps }] = useTable({
    title: '流程模版',
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    striped: false,
    useSearchForm: true,
    showTableSetting: true,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
  });

  /**
   * 设计窗口打开
   * @param record
   */
  function openDesign(record) {
    xmlData.value = record.modelContent;
    modelId.value = record.id;
    open.value = true;
  }
  /**
   * 设计窗口关闭
   * @param record
   */
  function closeDesign() {
    open.value = false;
    reload();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    isDisabled.value = false;
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    isDisabled.value = false;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteData({ id: record.id }, reload);
  }

  const customSearch = ref(false);
  function filterModelType(value) {
    const obj = typeArray.value.find((type) => type.value == value);
    return obj.text;
  }
  function getTypeArray() {
    const params = {};
    defHttp.get({ url: '/act/app/type/listAll', params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        res.result.forEach(function (obj) {
          typeArray.value.push({
            text: obj.typeName,
            value: obj.id,
          });
        });
      }
    });
  }
  onMounted(() => {
    //获取业务类型数组
    getTypeArray();
  });
  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less">
  .design-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
    }
  }
</style>
