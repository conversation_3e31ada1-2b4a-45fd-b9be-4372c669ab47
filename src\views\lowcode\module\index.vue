<template>
  <BasicTable @register="registerTable" :rowSelection="false">
    <template #tableTitle>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="handleAdd">新增</a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'operation'">
        <a @click="handleEdit(record)"><a-icon type="edit" style="margin-right: 3px" />编辑</a>
        <a-divider type="vertical" />
        <a-popconfirm title="是否删除？" ok-text="是" cancel-text="否" @confirm="handleDelete(record)">
          <a><DeleteOutlined style="margin-right: 3px; color: #eb2f96" /><span style="color: #eb2f96">删除</span></a>
        </a-popconfirm>
      </template>
    </template>
  </BasicTable>
  <ModuleModal @register="registerModal" @success="reload" :isDisabled="isDisabled" />
</template>

<script setup lang="ts" name="lowcode-module">
  import { ref } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { columns, searchFormSchema } from './module.data';
  import { getList, deleteModule } from './module.api';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import ModuleModal from './components/ModuleModal.vue';
  const [registerModal, { openModal }] = useModal();

  const isDisabled = ref(false);
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      api: getList,
      columns: columns,
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
      },
      //自定义默认排序
      defSort: {
        column: 'createTime',
        order: 'desc',
      },
      canResize: true, //可以自适应高度
      showActionColumn: false,
    },
  });

  const [registerTable, { reload }] = tableContext;
  /**
   * 新增事件
   */
  function handleAdd() {
    isDisabled.value = false;
    openModal(true, {
      isUpdate: false,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record) {
    isDisabled.value = false;
    openModal(true, {
      record,
      isUpdate: true,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteModule({ id: record.id }, reload);
  }
</script>

<style lang="less"></style>
