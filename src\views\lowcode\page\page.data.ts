import { BasicColumn, FormSchema } from '/@/components/Table';

export const searchFormSchema: FormSchema[] = [
  {
    field: 'pageName',
    label: '页面名称',
    component: 'JInput',
    colProps: { span: 6 },
  },
];
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'moduleId',
    label: 'moduleId',
    component: 'Input',
    show: false,
  },
  {
    field: 'pageName',
    label: '页面名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'description',
    label: '页面描述',
    component: 'InputTextArea',
    required: false,
  },
  {
    field: 'jsonSchema',
    label: 'JSON数据代码',
    component: 'InputTextArea',
    show: false,
  },
  {
    field: 'seq',
    label: '序号',
    component: 'InputNumber',
    required: false,
    componentProps: {
      placeholder: '请输入序号',
      style: { width: '100%' },
    },
  },
  {
    field: 'delFlag',
    label: '删除状态',
    component: 'Input',
    show: false,
  },
  {
    field: 'createBy',
    label: 'createBy',
    component: 'Input',
    show: false,
  },
  {
    field: 'createTime',
    label: 'createTime',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateBy',
    label: 'updateBy',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: 'updateTime',
    component: 'Input',
    show: false,
  },
];
export const columns: BasicColumn[] = [
  {
    title: '页面id',
    dataIndex: 'id',
  },
  {
    title: '页面名称',
    dataIndex: 'pageName',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '更新人',
    dataIndex: 'updateBy',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
  },
];
