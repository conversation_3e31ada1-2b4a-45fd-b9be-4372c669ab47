<template>
  <a-card>
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1">
        <template #tab>
          <span>
            <BarsOutlined />
            待审批
          </span>
        </template>
        <Todo />
      </a-tab-pane>
      <a-tab-pane key="2">
        <template #tab>
          <span>
            <CarryOutOutlined />
            已审批
          </span>
        </template>
        <Handle />
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>
<script lang="ts" setup>
  import { BarsOutlined, CarryOutOutlined } from '@ant-design/icons-vue';
  import { ref } from 'vue';
  import Todo from './todo.vue';
  import Handle from './handle.vue';
  const activeKey = ref('1');
</script>
