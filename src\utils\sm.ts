import base64js from 'base64-js';
import { sm2, sm4 } from 'sm-crypto';
const CIPHER_MODE = 1; // sm2 加密模式 1 - C1C3C2，0 - C1C2C3，默认为1
const hexDigits = 'abcdefghijklmn0123456789opqrstuvwxyz';
/**
 * 将字符串转换为十六进制字符串
 *
 * @param str 要转换的字符串
 * @returns 返回转换后的十六进制字符串
 */
export function stringToHex(str: string): string {
  if (str === '') {
    return '';
  }
  const hexCharCode: string[] = []; // 显式声明为 string[] 类型
  for (let i = 0; i < str.length; i++) {
    hexCharCode.push(str.charCodeAt(i).toString(16));
  }
  return hexCharCode.join('');
}
/**
 * sm2加密
 * @param {*} text 文本
 * @param {*} pubKey 公钥
 * @returns {string}
 */
export function encryptSm2(text, pubKey) {
  return '04' + sm2.doEncrypt(text, pubKey, CIPHER_MODE);
}
/**
 * sm2解密
 * @param {*} text 文本
 * @param {*} pubKey 公钥
 * @returns {string}
 */
export function decryptSm2(text, priKey) {
  return sm2.doDecrypt(text.substring(2), priKey);
}
/**
 * sm4加密，cbc 模式
 * @param {*} text 文本
 * @param {*} key 秘钥
 * @param {*} iv 偏移量
 * @returns {string}
 */
export function encryptSm4Base64(text, key, iv) {
  const data = sm4.encrypt(text, stringToHex(key), {
    mode: 'cbc',
    padding: 'pkcs#5',
    output: 'array',
    iv: stringToHex(iv),
  });
  return base64js.fromByteArray(data);
}
/**
 * sm4解密，cbc 模式
 * @param {*} text 文本
 * @param {*} key 秘钥
 * @param {*} iv 偏移量
 * @returns {string}
 */
export function decryptSm4Base64(text, key, iv) {
  const data = base64js.toByteArray(text);
  return sm4.decrypt(data, stringToHex(key), {
    mode: 'cbc',
    padding: 'pkcs#5',
    iv: stringToHex(iv),
  });
}

/**
 * 获取IV，m9hxruiqavnd0tpb
 * @returns
 */
export function getIv(): string {
  const arrayIndex: string[] = []; // 显式声明为 string[] 类型
  const pos = [22, 9, 17, 33, 27, 30, 18, 26, 10, 31, 23, 13, 0, 29, 25, 11];
  pos.forEach((val) => {
    arrayIndex.push(hexDigits.charAt(val));
  });
  return arrayIndex.join('');
}
/**
 * 获取sm4key
 * @returns
 */
export function getSm4Key(): string {
  const arrayIndex: string[] = []; // 显式声明为 string[] 类型
  const pos = [18, 11, 2, 22, 9, 4, 15, 16, 11, 17, 34, 10, 0, 35, 25, 12];
  pos.forEach((val) => {
    arrayIndex.push(hexDigits.charAt(val));
  });
  return arrayIndex.join('');
}

