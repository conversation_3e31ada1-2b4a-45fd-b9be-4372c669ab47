import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'seq',
    width: 80,
    resizable: true,
    sorter: {
      multiple: 1,
    },
  },
  {
    title: '业务编码',
    dataIndex: 'typeCode',
  },
  {
    title: '业务名称',
    dataIndex: 'typeName',
  },
  {
    title: '状态',
    dataIndex: 'state',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'typeName',
    label: '业务名称',
    component: 'JInput',
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'seq',
    label: '序号',
    component: 'InputNumber',
    defaultValue: 1,
    required: true,
    componentProps: {
      placeholder: '请输入序号',
      style: { width: '100%' },
    },
  },
  {
    field: 'typeCode',
    label: '业务编码',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入业务编码',
    },
  },
  {
    field: 'typeName',
    label: '业务名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入业务名称',
    },
  },
  {
    field: 'state',
    label: '状态',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      dictCode: 'biz_state',
      type: 'radioButton',
    },
    defaultValue: 'ACTIVE',
  },
];
