import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '工单名称',
    dataIndex: 'formName',
  },
  {
    title: '流程名称',
    dataIndex: 'procDefName',
  },
  {
    title: '发起时间',
    align: 'center',
    dataIndex: 'startTime',
    width: 200,
  },
  {
    title: '结束时间',
    align: 'center',
    dataIndex: 'endTime',
    width: 200,
  },
  {
    title: '流程耗时',
    align: 'center',
    dataIndex: 'duration',
    width: 200,
    customRender(obj) {
      const ms = obj.value;
      if (ms != null) {
        const day = Math.floor(ms / (24 * 60 * 60 * 1000));
        const hour = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
        const minute = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
        const second = Math.floor((ms % (60 * 1000)) / 1000);
        let val = '';
        if (day != 0) {
          val = val + day + '天';
        }
        if (hour != 0) {
          val = val + hour + '时';
        }
        if (minute != 0) {
          val = val + minute + '分';
        }
        if (second != 0) {
          val = val + second + '秒';
        }
        return val;
      } else {
        return ms;
      }
    },
  },
  {
    title: '完成状态',
    align: 'center',
    dataIndex: 'state',
    width: 150,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    field: 'formName',
    label: '工单名称',
    component: 'Input',
    colProps: { span: 8 },
  },
];
