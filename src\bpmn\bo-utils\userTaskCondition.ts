import { createModdleElement } from '/@/bpmn/utils/BpmnExtensionElementsUtil';
import modeler from '/@/bpmn/store/modeler';
export function setConditionValue(element, body) {
  let changeObj = {} as any;
  const formalExpressionElement = createModdleElement('bpmn:FormalExpression', { body });
  const businessObject = element?.businessObject;
  var loopCharacteristics = businessObject.loopCharacteristics;
  loopCharacteristics.completionCondition = formalExpressionElement;
  changeObj.loopCharacteristics = loopCharacteristics;
  const modeling = modeler().getModeling as any;
  modeling.updateModdleProperties(element, changeObj);
}
export function removeEqualSign(str) {
  if (str && str[0] === '=') {
    return str.substring(1);
  } else {
    return str;
  }
}
