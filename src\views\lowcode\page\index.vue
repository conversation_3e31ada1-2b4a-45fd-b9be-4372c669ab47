<template>
  <a-layout>
    <a-layout-sider :style="siderStyle" :width="siderWidth"><ModuleCard></ModuleCard></a-layout-sider>
    <a-layout-content :style="contentStyle"><PageTable></PageTable></a-layout-content>
  </a-layout>
</template>

<script setup lang="ts" name="lowcode-page">
  import { ref } from 'vue';
  import type { CSSProperties } from 'vue';
  import ModuleCard from './components/ModuleCard.vue';
  import PageTable from './components/PageTable.vue';
  import { createPageContext } from './pageContext';
  import mitt from '/@/utils/mitt';

  //事件总线定义--start
  const pageEmitter = mitt();
  createPageContext({
    pageEmitter: pageEmitter,
  });

  const siderWidth = ref(350);

  const contentStyle: CSSProperties = {
    textAlign: 'center',
    minHeight: 120,
    lineHeight: '120px',
    color: '#fff',
    backgroundColor: '#F5F5F5',
  };

  const siderStyle: CSSProperties = {
    textAlign: 'center',
    lineHeight: '120px',
    backgroundColor: '#F5F5F5',
  };
</script>

<style lang="less"></style>
