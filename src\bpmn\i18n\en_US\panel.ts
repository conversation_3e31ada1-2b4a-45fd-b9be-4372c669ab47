export default {
  general: 'General Information',
  id: 'ID',
  name: 'Name',
  version: 'Process Version',
  executable: 'Process Executable',

  documentationSettings: 'documentation Settings',
  documentationBody: 'Documentation',

  startInitiator: 'startInitiator',
  initiator: 'initiator',

  executionJob: 'executionJob',
  taskPriority: 'taskPriority',
  retryTimeCycle: 'retryTimeCycle',

  asyncContinuations: 'asyncContinuations',
  asyncBefore: 'asyncBefore',
  asyncAfter: 'asyncAfter',
  asyncExclusive: 'asyncExclusive',

  conditionalSettings: 'conditionalSettings',
  variableName: 'variableName',
  variableEvents: 'variableEvents',
  conditionType: 'conditionType',
  conditionExpression: 'conditionExpression',

  scriptType: 'scriptType',
  scriptLanguage: 'scriptLanguage',
  scriptBody: 'scriptBody',
  scriptResource: 'scriptResource',
  scriptFormat: 'scriptFormat',

  executionListeners: 'executionListeners',
  addExecutionListener: 'addExecutionListener',
  executionListenerEventType: 'executionListenerEventType',
  executionListenerType: 'executionListenerType',

  extensionProperties: 'extensionProperties',
  addExtensionProperties: 'addExtensionProperties',
  propertyName: 'propertyName',
  propertyValue: 'propertyValue',

  javaClass: 'javaClass',
  expression: 'expression',
  delegateExpression: 'delegateExpression',

  index: 'index',
  operations: 'operations',
  edit: 'edit',
  remove: 'remove',
  confirm: 'confirm'
}
