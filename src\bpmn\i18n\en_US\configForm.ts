export default {
  preferences: 'Preferences',
  language: 'Language',
  processName: 'Process Name',
  processId: 'Process ID',
  toolbar: 'Use Toolbar',
  miniMap: 'Use Minimap',
  useLint: 'Use Process Lint',
  templateChooser: 'Use Template Chooser',
  contextmenu: 'Use Contextmenu',
  customContextmenu: 'Use Custom Contextmenu',
  processEngine: 'Process Engine',
  background: 'Background',
  penalMode: 'Penal Mode',
  paletteMode: 'Palette Mode',
  contextPadMode: 'ContextPad Mode',
  rendererMode: 'Renderer Mode',
  otherModule: 'Other Modules',
  customTheme: 'Custom Theme',

  default: 'Default',
  rewrite: 'Rewrite',
  enhancement: 'Enhancement',
  custom: 'Custom',
  gridImage: 'Grid Image',
  grid: 'Grid Points',
  image: 'Image',
  none: 'None',

  camunda: 'Camunda',
  activiti: 'Activiti',
  flowable: 'Flowable',

  there_are_different_states_under_TemplateChooser:
    'There are different states under TemplateChooser'
}
