<template>
  <div>
    <a-row>
      <a-col :span="12">
        <div style="display: flex; justify-content: center">
          <a-card title="待选人员" size="small" style="width: 90%">
            <div style="margin-bottom: 20px">
              <a-form layout="inline" labelAlign="left">
                <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入人员姓名" v-model:value="queryParam.realname"></a-input>
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery"> 查询 </a-button>
                </a-form-item>
              </a-form>
            </div>
            <div>
              <a-table
                size="small"
                bordered
                :rowKey="rowKey"
                :columns="columns"
                :dataSource="dataSource"
                :pagination="ipagination"
                :loading="loading"
                :rowSelection="null"
                @change="handleTableChange"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <a v-show="record.isExist == 'N'" @click="addUser(record)">
                    <a-icon type="user-add" style="margin-right: 3px; color: green" /><span style="color: green">添加</span></a>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </div>
      </a-col>
      <a-col :span="12">
        <div style="display: flex; justify-content: center">
          <a-card :title="title" size="small" style="width: 90%">
            <div style="margin-bottom: 20px">
              <a-form layout="inline" labelAlign="left">
                <a-form-item label="姓名" :labelCol="labelCol" :wrapperCol="wrapperCol">
                  <a-input placeholder="请输入人员姓名" v-model:value="queryParam2.realname"></a-input>
                </a-form-item>
                <a-form-item>
                  <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery2"> 查询 </a-button>
                </a-form-item>
              </a-form>
            </div>
            <div>
              <a-table
                size="small"
                bordered
                :rowKey="rowKey"
                :columns="columns2"
                :dataSource="dataSource2"
                :pagination="false"
                :loading="loading2"
                :rowSelection="null"
                :scroll="{ y: 380 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'action'">
                    <a @click="delUser(record)">
                      <a-icon type="user-delete" style="margin-right: 3px; color: red" /><span style="color: red">移除</span></a
                    >
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { filterObj } from '/@/utils/common/compUtils';
  import { defHttp } from '/@/utils/http/axios';
  export default defineComponent({
    props: {
      userList: {
        type: Array,
        required: false,
        default: () => [],
      },
    },
    data() {
      return {
        labelCol: { xs: { span: 24 }, sm: { span: 5 } },
        wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
        queryParam: {},
        rowKey: 'id',
        /* 数据源 */
        dataSource: [],
        //表头
        columns: [],
        /* table加载状态 */
        loading: false,
        /* 分页参数 */
        ipagination: {
          current: 1,
          pageSize: 10,
          pageSizeOptions: ['10', '20', '30'],
          showTotal: (total, range) => {
            return range[0] + '-' + range[1] + ' 共' + total + '条';
          },
          showQuickJumper: true,
          showSizeChanger: true,
          total: 0,
        },
        //列定义
        defColumns: [
          {
            title: '序号',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t) {
              return parseInt(t.index) + 1;
            },
          },
          {
            title: '主键id',
            align: 'center',
            dataIndex: 'id',
            hidden: true,
          },
          {
            title: '是否已添加',
            dataIndex: 'isExist',
            hidden: true,
          },
          {
            title: '姓名',
            dataIndex: 'realname',
          },
          {
            title: '部门id',
            align: 'center',
            dataIndex: 'orgId',
            hidden: true,
          },
          {
            title: '部门',
            dataIndex: 'orgName',
          },
          {
            key: 'action',
            title: '操作',
            align: 'center',
            width: 100,
          },
        ],
        url: {
          list: '/act/app/model/userList',
          select: '/act/app/model/userSelect',
        },
        selUserIds: [],
        queryParam2: {},
        dataSource2: [],
        loading2: false,
        //表头
        columns2: [],
        //列定义
        defColumns2: [
          {
            title: '序号',
            key: 'rowIndex',
            width: 60,
            align: 'center',
            customRender: function (t) {
              return parseInt(t.index) + 1;
            },
          },
          {
            title: '主键id',
            align: 'center',
            dataIndex: 'id',
            hidden: true,
          },
          {
            title: '是否已添加',
            dataIndex: 'isExist',
            hidden: true,
          },
          {
            title: '姓名',
            dataIndex: 'realname',
          },
          {
            title: '部门id',
            align: 'center',
            dataIndex: 'orgId',
            hidden: true,
          },
          {
            title: '部门',
            dataIndex: 'orgName',
          },
          {
            key: 'action',
            title: '操作',
            align: 'center',
            width: 100,
          },
        ],
      };
    },
    methods: {
      refresh() {
        this.queryParam = [];
        this.queryParam2 = [];
        this.dataSource = [];
        this.dataSource2 = [];
        this.setSelUserIds();
        this.loadData(1);
        this.loadData2();
      },
      addUser(record) {
        this.selUserIds.push(record.id);
        this.dataSource2.unshift(record);
        let index = this.dataSource.findIndex((item) => item.id === record.id);
        if (index != -1) {
          record.isExist = 'Y';
          this.dataSource.splice(index, 1, record);
        }
      },
      delUser(record) {
        const indexToDelete = this.selUserIds.findIndex((id) => id === record.id);
        if (indexToDelete != -1) {
          this.selUserIds.splice(indexToDelete, 1);
        }

        const indexToDelete2 = this.dataSource2.findIndex((item) => item.id === record.id);
        if (indexToDelete2 != -1) {
          this.dataSource2.splice(indexToDelete2, 1);
        }

        let index = this.dataSource.findIndex((item) => item.id === record.id);
        if (index != -1) {
          record.isExist = 'N';
          this.dataSource.splice(index, 1, record);
        }
      },
      searchQuery() {
        this.loadData(1);
      },
      searchQuery2() {
        //
      },
      loadData2() {
        let params = { userIds: this.selUserIds.join(',') };
        this.loading2 = true;
        defHttp
          .get({ url: this.url.select, params }, { isTransformResponse: false })
          .then((res) => {
            if (res.success) {
              this.dataSource2 = res.result;
            } else {
              window.__messageBox.warning(res.message);
            }
          })
          .finally(() => {
            this.loading2 = false;
          });
      },
      loadData(arg) {
        let self = this;
        //加载数据 若传入参数1则加载第一页的内容
        if (arg === 1) {
          this.ipagination.current = 1;
        }
        var params = this.getQueryParams(); //查询条件
        this.loading = true;

        defHttp
          .get({ url: this.url.list, params }, { isTransformResponse: false })
          .then((res) => {
            if (res.success) {
              this.dataSource = res.result.records;
              //判断是否已添加
              this.dataSource.forEach(function (item) {
                if (self.selUserIds.includes(item.id)) {
                  item.isExist = 'Y';
                } else {
                  item.isExist = 'N';
                }
              });
              this.ipagination.total = res.result.total;
            } else {
              window.__messageBox.warning(res.message);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      getQueryParams() {
        let sqp = {};
        //获取查询条件
        var param = Object.assign(sqp, this.queryParam);
        param.pageNo = this.ipagination.current;
        param.pageSize = this.ipagination.pageSize;
        return filterObj(param);
      },
      handleTableChange(pagination) {
        //分页
        this.ipagination = pagination;
        this.loadData();
      },
      initColumns() {
        const cols = this.defColumns.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true;
          }
          if (!(item.hidden != null && item.hidden)) {
            return true;
          }
          return false;
        });
        this.columns = cols;
      },
      initColumns2() {
        const cols = this.defColumns2.filter((item) => {
          if (item.key == 'rowIndex' || item.dataIndex == 'action') {
            return true;
          }
          if (!(item.hidden != null && item.hidden)) {
            return true;
          }
          return false;
        });
        this.columns2 = cols;
      },
      setSelUserIds() {
        let self = this;
        this.selUserIds = [];
        this.userList.forEach(function (id) {
          self.selUserIds.push(id);
        });
      },
    },
    mounted() {
      this.refresh();
    },
    created() {
      this.initColumns();
      this.initColumns2();
    },
    watch: {
      userList: function () {
        this.setSelUserIds();
      },
    },
    computed: {
      title() {
        return '已选人员（' + this.selUserIds.length + '）';
      },
    },
  });
</script>

<style scoped></style>
