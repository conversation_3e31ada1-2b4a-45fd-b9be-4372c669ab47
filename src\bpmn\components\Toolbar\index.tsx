import { defineComponent, toRefs } from 'vue';
import { NButtonGroup } from 'naive-ui';
import Imports from '/@/bpmn/components/Toolbar/components/Imports';
import Exports from '/@/bpmn/components/Toolbar/components/Exports';
import Previews from '/@/bpmn/components/Toolbar/components/Previews';
import Aligns from '/@/bpmn/components/Toolbar/components/Aligns';
import Scales from '/@/bpmn/components/Toolbar/components/Scales';
import Commands from '/@/bpmn/components/Toolbar/components/Commands';
import ExternalTools from '/@/bpmn/components/Toolbar/components/ExternalTools';
import Deploy from '/@/bpmn/components/Toolbar/components/Deploy.vue';
const Toolbar = defineComponent({
  name: 'ToolBar',
  props: {
    modelId: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const { modelId } = toRefs(props);
    return () => (
      <div class="toolbar">
        <NButtonGroup>
          <Imports></Imports>
          <Exports></Exports>
          <Previews></Previews>
        </NButtonGroup>
        <Aligns></Aligns>
        <Scales></Scales>
        <Commands></Commands>
        <ExternalTools></ExternalTools>
        <Deploy modelId={modelId.value}></Deploy>
      </div>
    );
  },
});

export default Toolbar;
