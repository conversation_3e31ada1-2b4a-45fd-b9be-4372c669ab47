<template>
  <div>
    <BasicTable @register="registerTable" :class="{ 'p-4': customSearch }">
      <template #action="{ record }">
        <a @click="openCheckInfoModal(record)"> <BranchesOutlined style="margin-right: 3px; color: green" /><span style="color: green">详情</span></a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定撤回至申请节点吗?" @confirm="() => recallTask(record)">
          <a> <RollbackOutlined style="margin-right: 3px; color: #1890ff" /><span style="color: #1890ff">撤回</span></a>
        </a-popconfirm>
        <a-divider type="vertical" />
        <a-popconfirm title="确定取消当前流程吗?" @confirm="() => cancleProcInst(record)">
          <a> <DeleteOutlined style="margin-right: 3px; color: #eb2f96" /><span style="color: #eb2f96">取消</span></a>
        </a-popconfirm>
      </template>
      <template #bodyCell="{ column, text }">
        <template v-if="column.key === 'state'">
          <span>
            <a-tag v-if="text == 'ACTIVE'" color="pink">未完成</a-tag>
            <a-tag v-if="text == 'COMPLETED'" color="green">已完成</a-tag>
          </span>
        </template>
      </template>
    </BasicTable>
    <!-- table区域-end -->
    <a-modal
      v-if="open"
      v-model:open="open"
      title="流程窗口"
      @cancel="closeModal"
      :footer="null"
      :keyboard="false"
      width="100%"
      wrap-class-name="flow-modal"
    >
      <CheckInfo
        :allowOperate="checkInfo.allowOperate"
        :startUserName="checkInfo.startUserName"
        :procDefKey="checkInfo.procDefKey"
        :procDefName="checkInfo.procDefName"
        :procInstId="checkInfo.procInstId"
        :businessKey="checkInfo.businessKey"
      ></CheckInfo>
    </a-modal>
  </div>
</template>

<script setup lang="ts" name="camunda-initiation">
  import { ref, unref, watch, reactive } from 'vue';
  //列表组件
  import { BasicTable, useTable } from '/@/components/Table';
  import { BasicColumn } from '/@/components/Table';
  import { FormSchema } from '/@/components/Table';
  import { BranchesOutlined, RollbackOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { defHttp } from '/@/utils/http/axios';
  import CheckInfo from '@/views/camunda/use/checkInfo/index.vue';
  //提示框组件对象
  const { createMessage } = useMessage();
  /**
   * 后台交互路径
   */
  enum Api {
    list = '/act/manage/initiationList',
    recallTask = '/act/manage/recall',
    delete = '/act/manage/delete',
  }
  //窗口开关
  const open = ref(false);
  //窗口参数对象
  const checkInfo = reactive({
    startUserName: '', //发起人
    procDefKey: '', //流程key
    procDefName: '', //流程名称
    procInstId: '', //实例id
    businessKey: '', //业务主键
    allowOperate: false, //是否允许操作
  });
  const getList = (params) => {
    return defHttp.get({ url: Api.list, params });
  };
  const columns: BasicColumn[] = [
    {
      title: '流程名称 ',
      align: 'center',
      dataIndex: 'procDefName',
    },
    {
      title: '发起时间',
      align: 'center',
      dataIndex: 'startTime',
      width: 250,
    },
    {
      title: '结束时间',
      align: 'center',
      dataIndex: 'endTime',
      width: 250,
    },
    {
      title: '流程耗时',
      align: 'center',
      dataIndex: 'duration',
      width: 250,
      customRender(obj) {
        const ms = obj.value;
        if (ms != null) {
          const day = Math.floor(ms / (24 * 60 * 60 * 1000));
          const hour = Math.floor((ms % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
          const minute = Math.floor((ms % (60 * 60 * 1000)) / (60 * 1000));
          const second = Math.floor((ms % (60 * 1000)) / 1000);
          let val = '';
          if (day != 0) {
            val = val + day + '天';
          }
          if (hour != 0) {
            val = val + hour + '时';
          }
          if (minute != 0) {
            val = val + minute + '分';
          }
          if (second != 0) {
            val = val + second + '秒';
          }
          return val;
        } else {
          return ms;
        }
      },
    },
    {
      title: '完成状态',
      align: 'center',
      dataIndex: 'state',
      width: 200,
    },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      field: 'procDefName',
      label: '流程名称',
      component: 'Input',
      colProps: { span: 8 },
    },
  ];
  const [registerTable, { reload, setProps }] = useTable({
    api: getList,
    columns,
    formConfig: {
      //labelWidth: 120,
      schemas: searchFormSchema,
      autoAdvancedCol: 2,
      actionColOptions: {
        style: { textAlign: 'left' },
      },
    },
    striped: true,
    useSearchForm: true,
    showTableSetting: false,
    clickToRowSelect: false,
    bordered: true,
    showIndexColumn: true,
    tableSetting: { fullScreen: true },
    canResize: false,
    rowKey: 'id',
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  /**
   * 打开详情窗口
   */
  function openCheckInfoModal(record) {
    checkInfo.businessKey = record.businessKey;
    checkInfo.procInstId = record.procInstId;
    checkInfo.procDefKey = record.procDefKey;
    checkInfo.procDefName = record.procDefName;
    checkInfo.startUserName = record.startUserName;
    open.value = true;
  }
  /**
   * 撤回只申请节点
   */
  function recallTask(record) {
    if (record.state == 'COMPLETED') {
      createMessage.warning('当前流程已完成，无法撤回！');
      return false;
    }
    let params = {
      procInstId: record.procInstId,
      comment: '申请人撤回',
      opType: 'recall',
      opDesc: '撤回',
    };
    //提交表单
    defHttp.post({ url: Api.recallTask, params: params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        reload();
      } else {
        createMessage.warning(res.message);
      }
    });
  }
  /**
   * 取消流程实例
   */
  function cancleProcInst(record) {
    if (record.state == 'COMPLETED') {
      createMessage.warning('当前流程已完成，无法取消！');
      return false;
    }
    let params = {
      procInstId: record.procInstId,
    };
    defHttp.post({ url: Api.delete, params: params }, { isTransformResponse: false }).then((res) => {
      if (res.success) {
        createMessage.success(res.message);
        reload();
      } else {
        createMessage.warning(res.message);
      }
    });
  }
  /**
   * 流程窗口关闭
   * @param record
   */
  function closeModal() {
    open.value = false;
  }

  const customSearch = ref(false);

  watch(customSearch, () => {
    setProps({ useSearchForm: !unref(customSearch) });
  });
</script>
<style lang="less">
  .flow-modal {
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }

    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }

    .ant-modal-body {
      flex: 1;
      padding: 20px;
    }
  }
</style>
