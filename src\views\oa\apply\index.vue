<template>
  <div class="oa-apply">
    <a-card v-for="type in typeArray" :key="type.id" :title="type.typeName" style="margin-bottom: 20px">
      <a-card-grid v-for="form in type.formList" :key="form.id" style="width: 20%; text-align: center" @click="formClick(form)">
        <Icon :icon="form.icon" size="30" class="icon" />
        <span class="text">{{ form.formName }}</span>
      </a-card-grid>
    </a-card>
  </div>
  <FormModal @register="registerModal" :title="title" :feat="feat" />
</template>

<script setup lang="ts" name="oa-apply">
  import { onMounted, reactive, ref } from 'vue';
  import Icon from '/@/components/Icon/src/Icon.vue';
  import { defHttp } from '/@/utils/http/axios';

  import { useModal } from '/@/components/Modal';
  import FormModal from '/@/views/oa/components/FormModal.vue';
  const [registerModal, { openModal }] = useModal();
  //表单类型列表
  const typeArray = reactive<any[]>([]);
  //窗口属性
  const title = ref<string>('');
  const feat = ref<string>('Insert');

  const formClick = (form) => {
    title.value = form.formName;
    openModal(true, { processKey: form.modelKey, formId: form.id, businessKey: '' });
  };
  /**
   * 加载表单列表
   *
   * @returns 无返回值
   */
  const loadForm = async () => {
    const res = await defHttp.get({ url: '/dwf/form/listAuth' }, { isTransformResponse: false });
    if (res.success) {
      Object.assign(typeArray, res.result);
    }
  };
  /**
   * 组件挂载完成后执行
   */
  onMounted(() => {
    loadForm();
  });
</script>

<style scoped lang="less">
  .oa-apply {
    background-color: #fff;
    padding: 20px;
    :deep(.ant-card .ant-card-head) {
      display: flex;
      justify-content: center;
      flex-direction: column;
      min-height: 56px;
      margin-bottom: -1px;
      padding: 0 24px;
      color: rgba(51, 51, 51, 0.88);
      font-weight: 600;
      font-size: 16px;
      background: transparent;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 4px 4px 0 0;
      background-color: #eaeaea;
    }
  }
  .icon {
    display: inline-flex;
    vertical-align: middle; /* 垂直方向中间对齐 */
  }
  .text {
    margin-left: 20px;
    display: inline-block; /* 行内块元素 */
    vertical-align: middle; /* 垂直方向中间对齐 */
  }
</style>
