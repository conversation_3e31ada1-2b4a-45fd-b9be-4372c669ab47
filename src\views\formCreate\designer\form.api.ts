import { defHttp } from '/@/utils/http/axios';

import { ref } from 'vue';

const typeArray = ref<any>([]);
const modelArray = ref<any>([]);

export enum Api {
  getList = '/dwf/form/list',
  save = '/dwf/form/add',
  edit = '/dwf/form/edit',
  delete = '/dwf/form/delete',
  getById = '/dwf/form/queryById',
  getType = '/dwf/type/listAll',
  getModel = '/act/app/model/listAll',
}
/**
 * 查询表单详情
 * @param params
 */
export const getById = (params) => {
  return defHttp.get({ url: Api.getById, params });
};
/**
 * 查询表单列表
 * @param params
 */
export const getList = (params) => defHttp.get({ url: Api.getList, params });
/**
 * 保存或者更新主类
 */
export const saveOrUpdateProp = (params, isUpdate) => {
  if (isUpdate) {
    return defHttp.post({ url: Api.edit, params });
  } else {
    return defHttp.post({ url: Api.save, params });
  }
};
/**
 * 删除模型表单
 * @param params
 */
export const deleteForm = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 查询所有业务类型
 */
export const loadType = () => {
  const params = {};
  defHttp.get({ url: Api.getType, params }, { isTransformResponse: false }).then((res) => {
    if (res.success) {
      res.result.forEach(function (obj) {
        typeArray.value.push({
          label: obj.typeName,
          value: obj.id,
        });
      });
    }
  });
};
export const filterType = (value) => {
  const obj = typeArray.value.find((type) => type.value == value);
  return obj.label;
};
export const getTypeArray = () => {
  return typeArray.value;
};
/**
 * 查询所有流程类型
 */
export const loadModel = () => {
  const params = {};
  defHttp.get({ url: Api.getModel, params }, { isTransformResponse: false }).then((res) => {
    if (res.success) {
      res.result.forEach(function (obj) {
        modelArray.value.push({
          label: obj.modelName,
          value: obj.modelKey,
        });
      });
    }
  });
};
export const filterModel = (value) => {
  const obj = modelArray.value.find((model) => model.value == value);
  return obj.label;
};
export const getModelArray = () => {
  return modelArray.value;
};
