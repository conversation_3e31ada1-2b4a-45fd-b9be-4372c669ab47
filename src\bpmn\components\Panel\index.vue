<template>
  <div ref="panel" class="panel"
    ><div class="panel-header">
      <BpmnIcon :name="bpmnIconName"></BpmnIcon>
      <p>{{ bpmnElementName }}</p>
      <p>{{ customTranslate(currentElementType || 'Process') }}</p>
    </div>
    <n-collapse arrow-placement="right" :expanded-names="activeNames" @item-header-click="handleItemHeaderClick">
      <!-- 常规信息 -->
      <ElementGenerations v-if="componentNameArray.includes(compEnum.ElementGenerations)" />
      <!-- 文档设置 -->
      <ElementDocumentations v-if="componentNameArray.includes(compEnum.ElementDocumentations)" />
      <!-- 条件设置 -->
      <ElementConditional v-if="componentNameArray.includes(compEnum.ElementConditional)" />
      <!-- 执行作业 -->
      <ElementJobExecution v-if="componentNameArray.includes(compEnum.ElementJobExecution)" />
      <!-- 扩展属性 -->
      <ElementExtensionProperties v-if="componentNameArray.includes(compEnum.ElementExtensionProperties)" />
      <!-- 执行监听器 -->
      <ElementExecutionListeners v-if="componentNameArray.includes(compEnum.ElementExecutionListeners)" />
      <!-- 异步控制 -->
      <ElementAsyncContinuations v-if="componentNameArray.includes(compEnum.ElementAsyncContinuations)" />
      <!-- 启动器 -->
      <ElementStartInitiator v-if="componentNameArray.includes(compEnum.ElementStartInitiator)" />
      <!-- 表列设置 -->
      <ElementProcessTable v-if="componentNameArray.includes(compEnum.ElementProcessTable)" />
      <!-- 人员抄送 -->
      <ElementProcessCopy v-if="componentNameArray.includes(compEnum.ElementProcessCopy)" />
      <!-- 多实例条件 -->
      <ElementTaskCondition v-if="componentNameArray.includes(compEnum.ElementTaskCondition)" />
      <!-- 候选人员 -->
      <ElementTaskUser v-if="componentNameArray.includes(compEnum.ElementTaskUser)" />
      <!-- 操作权限 -->
      <ElementTaskButton v-if="componentNameArray.includes(compEnum.ElementTaskButton)" />
      <!-- 条件参数 -->
      <ElementTaskVar v-if="componentNameArray.includes(compEnum.ElementTaskVar)" />
      <!--业务任务-->
      <ElementImplementation v-if="componentNameArray.includes(compEnum.ElementImplementation)" />
    </n-collapse>
  </div>
</template>

<script setup lang="ts">
  import { markRaw, onMounted, ref, reactive } from 'vue';
  import { NCollapse } from 'naive-ui';
  import { Element, Connection, Label, Shape } from 'diagram-js/lib/model/Types';
  import { Translate } from 'diagram-js/lib/i18n/translate';
  import debounce from 'lodash.debounce';

  import EventEmitter from '/@/bpmn/utils/EventEmitter';
  import modelerStore from '/@/bpmn/store/modeler';
  import Logger from '/@/bpmn/utils/Logger';

  import getBpmnIconType from '/@/bpmn/bpmn-icons/getIconType';
  import bpmnIcons from '/@/bpmn/bpmn-icons';
  import BpmnIcon from '/@/bpmn/components/common/BpmnIcon.vue';

  import { isAsynchronous } from '/@/bpmn/bo-utils/asynchronousContinuationsUtil';
  import { isExecutable } from '/@/bpmn/bo-utils/executionListenersUtil';
  import { isJobExecutable } from '/@/bpmn/bo-utils/jobExecutionUtil';
  import { isStartInitializable, isUserAssignmentSupported } from '/@/bpmn/bo-utils/initiatorUtil';

  import ElementGenerations from './components/ElementGenerations.vue';
  import ElementConditional from './components/ElementConditional.vue';
  import ElementDocumentations from './components/ElementDocumentations.vue';
  import ElementExecutionListeners from './components/ElementExecutionListeners.vue';
  import ElementExtensionProperties from './components/ElementExtensionProperties.vue';
  import ElementAsyncContinuations from './components/ElementAsyncContinuations.vue';
  import ElementJobExecution from './components/ElementJobExecution.vue';
  import ElementStartInitiator from './components/ElementStartInitiator.vue';

  // import UserAssignment from './components/UserAssignment.vue';

  import { isCanbeConditional } from '/@/bpmn/bo-utils/conditionUtil';
  import { customTranslate } from '/@/bpmn/additional-modules/Translate';
  //自定义扩展配置
  import ElementProcessTable from './components/ElementProcessTable.vue';
  import ElementProcessCopy from './components/ElementProcessCopy.vue';
  import ElementTaskUser from './components/ElementTaskUser.vue';
  import ElementTaskButton from './components/ElementTaskButton.vue';
  import ElementTaskVar from './components/ElementTaskVar.vue';
  import ElementTaskCondition from './components/ElementTaskCondition.vue';
  import ElementImplementation from './components/ElementImplementation.vue';
  //自定义扩展工具类
  import { isProcessElement, isMultiUserTask, isServiceTask } from '/@/bpmn/bo-utils/processTypeUtil';
  import type { CollapseProps } from 'naive-ui';

  enum compEnum {
    ElementGenerations = 'ElementGenerations',
    ElementTaskVar = 'ElementTaskVar',
    ElementTaskCondition = 'ElementTaskCondition',
    ElementDocumentations = 'ElementDocumentations',
    ElementConditional = 'ElementConditional',
    ElementJobExecution = 'ElementJobExecution',
    ElementExtensionProperties = 'ElementExtensionProperties',
    ElementExecutionListeners = 'ElementExecutionListeners',
    ElementAsyncContinuations = 'ElementAsyncContinuations',
    ElementStartInitiator = 'ElementStartInitiator',
    ElementProcessTable = 'ElementProcessTable',
    ElementProcessCopy = 'ElementProcessCopy',
    ElementTaskUser = 'ElementTaskUser',
    ElementTaskButton = 'ElementTaskButton',
    ElementImplementation = 'ElementImplementation',
  }

  const modeler = modelerStore();
  const panel = ref<HTMLDivElement | null>(null);
  const currentElementId = ref<string | undefined>(undefined);
  const currentElementType = ref<string | undefined>(undefined);

  const penalTitle = ref<string | undefined>('属性配置');
  const bpmnIconName = ref<string>('Process');
  const bpmnElementName = ref<string>('Process');

  const componentNameArray = reactive<any>([]);
  const activeNames = reactive([]);

  const userTasks = reactive<any>([]);

  const setCurrentComponents = (element: BpmnElement, oldElementId) => {
    // 清空
    componentNameArray.splice(0, componentNameArray.length);
    // 重设
    //用户任务
    setUserTask(element);
    //常规信息
    componentNameArray.push(compEnum.ElementGenerations);
    //文档设置
    componentNameArray.push(compEnum.ElementDocumentations);
    //条件设置
    isCanbeConditional(element) && componentNameArray.push(compEnum.ElementConditional);
    //执行作业
    isJobExecutable(element) && componentNameArray.push(compEnum.ElementJobExecution);
    //扩展属性
    componentNameArray.push(compEnum.ElementExtensionProperties);
    //执行监听器
    isExecutable(element) && componentNameArray.push(compEnum.ElementExecutionListeners);
    //异步控制
    isAsynchronous(element) && componentNameArray.push(compEnum.ElementAsyncContinuations);
    //启动器
    isStartInitializable(element) && componentNameArray.push(compEnum.ElementStartInitiator);
    //自定义扩展
    //process-表列设置
    isProcessElement(element) && componentNameArray.push(compEnum.ElementProcessTable);
    //process-人员抄送
    isProcessElement(element) && componentNameArray.push(compEnum.ElementProcessCopy);
    //多实例条件
    isMultiUserTask(element) && componentNameArray.push(compEnum.ElementTaskCondition);
    //用户任务-候选人员
    isUserAssignmentSupported(element) && componentNameArray.push(compEnum.ElementTaskUser);
    //用户任务-按钮权限
    isUserAssignmentSupported(element) && componentNameArray.push(compEnum.ElementTaskButton);
    //用户任务-条件参数
    isUserAssignmentSupported(element) && componentNameArray.push(compEnum.ElementTaskVar);
    //业务任务
    isServiceTask(element) && componentNameArray.push(compEnum.ElementImplementation);
    //切换元素面板收缩
    if (!(typeof oldElementId === 'undefined') && element.id != oldElementId) {
      // 清空数组
      activeNames.length = 0;
    }
  };

  // 设置选中元素，更新 store
  const setCurrentElement = debounce((element: Shape | Element | Connection | Label | null) => {
    const oldElementId = currentElementId.value;
    let activatedElement: BpmnElement | undefined = element;
    let activatedElementTypeName = '';

    if (!activatedElement) {
      activatedElement =
        modeler.getElRegistry?.find((el) => el.type === 'bpmn:Process') || modeler.getElRegistry?.find((el) => el.type === 'bpmn:Collaboration');

      if (!activatedElement) {
        return Logger.prettyError('No Element found!');
      }
    }
    activatedElementTypeName = getBpmnIconType(activatedElement);

    modeler.setElement(markRaw(activatedElement));
    currentElementId.value = activatedElement.id;
    currentElementType.value = activatedElement.type.split(':')[1];

    penalTitle.value = modeler.getModeler?.get<Translate>('translate')(currentElementType.value);
    bpmnIconName.value = bpmnIcons[activatedElementTypeName];
    bpmnElementName.value = activatedElementTypeName;

    setCurrentComponents(activatedElement, oldElementId);
    EventEmitter.emit('element-update', activatedElement);

    // Logger.prettyPrimary('Selected element changed', `ID: ${activatedElement.id} , type: ${activatedElement.type}`);
  }, 100);
  const setUserTask = (element) => {
    const modeling = modeler.getModeling as any;
    //找到指定id的元素
    const taskIndex = userTasks.findIndex((task) => task.id === element.id);
    if (taskIndex == -1) {
      if (element.type == 'bpmn:UserTask') {
        let changeElement = {} as any;
        changeElement.assignee = '${' + element.id + '_user}';
        let isMulti = false;
        const businessObject = element.businessObject;
        //多实例
        if (businessObject.hasOwnProperty('loopCharacteristics')) {
          isMulti = true;
          var loopCharacteristics = businessObject.loopCharacteristics;
          loopCharacteristics.collection = '${' + element.id + '_list}';
          loopCharacteristics.elementVariable = element.id + '_user';
          changeElement.loopCharacteristics = loopCharacteristics;
        }
        modeling.updateProperties(element, changeElement);
        userTasks.push({
          id: element.id,
          isMulti: isMulti,
        });
      }
    } else {
      if (element.type == 'bpmn:UserTask') {
        const userTask = userTasks[taskIndex];
        const businessObject = element.businessObject;
        let isMulti = businessObject.hasOwnProperty('loopCharacteristics');
        //如果用户任务实例类型发生变化
        if (userTask.isMulti != isMulti) {
          let changeElement = {} as any;
          changeElement.assignee = '${' + element.id + '_user}';
          if (isMulti) {
            var loopCharacteristics = businessObject.loopCharacteristics;
            loopCharacteristics.collection = '${' + element.id + '_list}';
            loopCharacteristics.elementVariable = element.id + '_user';
            changeElement.loopCharacteristics = loopCharacteristics;
          }
          modeling.updateProperties(element, changeElement);
          userTasks.splice(taskIndex, 1);
          userTasks.push({
            id: element.id,
            isMulti: isMulti,
          });
        }
      } else {
        userTasks.splice(taskIndex, 1);
      }
    }
  };
  EventEmitter.on('modeler-init', (modeler) => {
    // 导入完成后默认选中 process 节点
    modeler.on('import.done', () => {
      setCurrentElement(null);
    });
    // 监听选择事件，修改当前激活的元素以及表单
    modeler.on('selection.changed', ({ newSelection }) => {
      setCurrentElement(newSelection[0] || null);
      // setUserTask(newSelection[0]);
    });
    modeler.on('element.changed', ({ element }) => {
      // 保证 修改 "默认流转路径" 等类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。
      if (element && element.id === currentElementId.value) {
        setCurrentElement(element);
        // setUserTask(element);
      }
    });
    //元素销毁
    modeler.on('shape.removed', (element) => {
      const taskIndex = userTasks.findIndex((task) => task.id === element.id);
      if (taskIndex != -1) {
        userTasks.splice(taskIndex, 1);
      }
      currentElementId.value = undefined;
      setCurrentElement(null);
    });
    modeler.on('element.click', () => {
      // Logger.prettyInfo('Element Click', event);
    });
  });

  const handleItemHeaderClick: CollapseProps['onItemHeaderClick'] = ({ name, expanded }) => {
    if (expanded) {
      activeNames.push(name);
    } else {
      const indexToRemove = activeNames.indexOf(name);
      if (indexToRemove !== -1) {
        activeNames.splice(indexToRemove, 1);
      }
    }
  };
  onMounted(() => !currentElementId.value && setCurrentElement(null));
</script>

<style scoped></style>
